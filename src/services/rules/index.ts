import { ExtensionRulesShape } from "shared/lib/bridge/protocol";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { FromIdeProtocol } from "shared/lib/LocalService";
import { LocalService } from "../../core/localService";
import * as path from "path";
import * as os from "os";
import { WorkspaceStateManager } from "../../base/state-manager";
import { WorkspaceState } from "shared/lib/state-manager/types";
import { isRuleFile } from "shared/lib/util";
import { KWAIPILOT_RULES_EXT } from "shared/lib/const";
import { WebloggerManager } from "../../base/weblogger";
import { KwaipilotEnv } from "../../const";

export class RulesService extends ServiceModule implements ExtensionRulesShape {
  private decorationType: vscode.TextEditorDecorationType;
  private fileChangeListener: vscode.Disposable | undefined;
  private editorChangeListener: vscode.Disposable | undefined;
  private ruleFilesWatcher: vscode.FileSystemWatcher | undefined;
  ruleSet: Set<string> = new Set();
  private docCommand = "kwaipilot.rules.openLink1";

  constructor(ext: ContextManager) {
    super(ext);

    // 注册命令
    const disposable1 = vscode.commands.registerCommand(this.docCommand, () => {
      vscode.env.openExternal(vscode.Uri.parse("https://docs.corp.kuaishou.com/d/home/<USER>"));
    });

    // 添加到context中以便后续释放
    this.ext.context.subscriptions.push(disposable1);

    this.decorationType = vscode.window.createTextEditorDecorationType({
      after: {
        margin: "0 0 0 0",
        textDecoration: "none",
      },
      rangeBehavior: vscode.DecorationRangeBehavior.ClosedOpen,
    });

    this.registerMdrFileListeners();
    this.initRuleFilesWatcher();
    this.initRuleFiles();
  }

  $getRules(): string[] {
    return [...this.ruleSet];
  }

  private registerMdrFileListeners(): void {
    this.fileChangeListener = vscode.workspace.onDidChangeTextDocument((event) => {
      const document = event.document;
      if (isRuleFile(document.fileName)) {
        this.updateDecorations(document);
      }
    });

    this.editorChangeListener = vscode.window.onDidChangeActiveTextEditor((editor) => {
      if (editor && isRuleFile(editor.document.fileName)) {
        this.updateDecorations(editor.document);
      }
    });

    if (vscode.window.activeTextEditor
      && isRuleFile(vscode.window.activeTextEditor.document.fileName)) {
      this.updateDecorations(vscode.window.activeTextEditor.document);
    }
  }

  // 更新装饰器
  private updateDecorations(document: vscode.TextDocument): void {
    // 获取所有编辑器
    const editors = vscode.window.visibleTextEditors.filter(
      editor => editor.document.uri.toString() === document.uri.toString(),
    );

    if (editors.length === 0) {
      return;
    }

    // 检查文档是否为空
    if (document.getText().trim() === "") {
      // 创建可点击的markdown字符串
      const link1 = new vscode.MarkdownString(`[规则配置说明](command:${this.docCommand})`);

      // 允许命令链接
      link1.isTrusted = true;

      const decorations: vscode.DecorationOptions[] = [
        {
          range: new vscode.Range(0, 0, 0, 0),
          renderOptions: {
            after: {
              contentText: "填写示例：1.在生成代码时添加函数级注释。 注意：本文件最多不超过5000个字符。hover文本可点击“规则配置说明”跳转查看具体说明。",
              color: "",
              textDecoration: "cursor: pointer;",
            },
          },
          hoverMessage: link1,
        },
      ];
      // 清除其他插件的装饰器
      editors.forEach((editor) => {
        editor.setDecorations(this.decorationType, []);
      });
      // 应用装饰器
      editors.forEach((editor) => {
        editor.setDecorations(this.decorationType, decorations);
      });
    }
    else {
      // 文档不为空，清除装饰器
      editors.forEach((editor) => {
        editor.setDecorations(this.decorationType, []);
      });
    }
  }

  // 初始化规则文件监听器
  private async initRuleFilesWatcher(): Promise<void> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      return;
    }

    const rootPath = workspaceFolders[0].uri.fsPath;
    const projectRulesDir = path.join(rootPath, ".kwaipilot", "rules");
    const projectDirUri = vscode.Uri.file(projectRulesDir);

    try {
      // 检查目录是否存在
      await vscode.workspace.fs.stat(projectDirUri);

      // 目录存在，创建监听器
      this.createFileWatcher(projectDirUri);
    }
    catch (err) {
      // 目录不存在，不创建监听器
    }
  }

  // 创建文件系统监听器
  private createFileWatcher(projectDirUri: vscode.Uri): void {
    // 如果已有监听器，先清理
    if (this.ruleFilesWatcher) {
      this.ruleFilesWatcher.dispose();
    }

    // 创建文件系统监听器，监听 .mdr 文件的变化
    this.ruleFilesWatcher = vscode.workspace.createFileSystemWatcher(
      new vscode.RelativePattern(projectDirUri, `**/*${KWAIPILOT_RULES_EXT}`),
    );

    // 监听文件创建事件
    this.ruleFilesWatcher.onDidCreate((e) => {
      const path = vscode.workspace.asRelativePath(e.fsPath);
      this.ruleSet.add(path);
      this.workspaceState.update(WorkspaceState.RULE_FILE_LIST, [...this.ruleSet]);
    });

    // 监听文件删除事件
    this.ruleFilesWatcher.onDidDelete((e) => {
      const path = vscode.workspace.asRelativePath(e.fsPath);
      this.ruleSet.delete(path);
      this.workspaceState.update(WorkspaceState.RULE_FILE_LIST, [...this.ruleSet]);
    });

    // 将监听器添加到上下文中以便后续释放
    this.ext.context.subscriptions.push(this.ruleFilesWatcher);
  }

  // 更新规则文件列表
  private async initRuleFiles() {
    try {
      const result: string[] = [];
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (workspaceFolders && workspaceFolders.length > 0) {
        const rootPath = workspaceFolders[0].uri.fsPath;
        const projectRulesDir = path.join(rootPath, ".kwaipilot", "rules");
        const projectDirUri = vscode.Uri.file(projectRulesDir);

        try {
          // 检查项目规则目录是否存在
          await vscode.workspace.fs.stat(projectDirUri);

          // 递归获取规则文件
          await this.collectRuleFiles(projectDirUri, ".kwaipilot/rules", result);
        }
        catch (err) {
          // 项目规则目录不存在，忽略错误
        }
      }
      this.ruleSet = new Set(result.slice(0, 1000));

      if (KwaipilotEnv.isInIde) {
        // 在ide中由于顺序不同，需要通过emit来更新规则文件列表
        this.workspaceState.emit(WorkspaceState.RULE_FILE_LIST, [...this.ruleSet]);
      }
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`更新规则文件列表失败: ${error.message || error}`);
    }
  }

  // 销毁时清理资源
  dispose(): void {
    this.decorationType.dispose();
    if (this.fileChangeListener) {
      this.fileChangeListener.dispose();
    }
    if (this.editorChangeListener) {
      this.editorChangeListener.dispose();
    }
    if (this.ruleFilesWatcher) {
      this.ruleFilesWatcher.dispose();
    }
  }

  async $openUserRule() {
    try {
      // 用户规则文件路径
      const userHomeDir = os.homedir();
      const kwaipilotDir = path.join(userHomeDir, ".kwaipilot", "rules");
      const ruleFilePath = path.join(kwaipilotDir, "user_rules.mdr");

      // 创建 URI
      const dirUri = vscode.Uri.file(kwaipilotDir);
      const fileUri = vscode.Uri.file(ruleFilePath);

      try {
        // 检查文件是否存在
        await vscode.workspace.fs.stat(fileUri);

        // 如果文件存在，直接打开
        const document = await vscode.workspace.openTextDocument(fileUri);
        await vscode.window.showTextDocument(document);
      }
      catch (err) {
        // 文件不存在，先确保目录存在
        try {
          await vscode.workspace.fs.stat(dirUri);
        }
        catch {
          // 目录不存在，创建目录
          await vscode.workspace.fs.createDirectory(dirUri);
        }

        // 创建文件内容
        const content = "";
        const contentBuffer = Buffer.from(content, "utf8");

        // 写入文件
        await vscode.workspace.fs.writeFile(fileUri, contentBuffer);
        // vscode.window.showInformationMessage("已创建用户规则文件");

        // 打开新创建的文件
        const document = await vscode.workspace.openTextDocument(fileUri);
        await vscode.window.showTextDocument(document);
      }
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`打开用户规则文件失败: ${error.message || error}`);
    }
    finally {
      this.weblogger.$reportUserAction<"rule_setting">({
        key: "rule_setting",
        type: "user_rule",
      });
    }
  }

  async $openProjectRules() {
    try {
      const workspaceFolders = vscode.workspace.workspaceFolders;

      if (!workspaceFolders || workspaceFolders.length === 0) {
        vscode.window.showInformationMessage("请先打开一个项目");
        return;
      }

      const rootPath = workspaceFolders[0].uri.fsPath;

      const fileName = await vscode.window.showInputBox({
        placeHolder: "仅支持英文小写字母、数字、- 和 _",
        validateInput: (value: string) => {
          if (!/^[a-z0-9_-]+$/.test(value)) {
            return "仅支持英文小写字母、数字、- 和 _";
          }
          if (value.length > 100) {
            return "文件名最大长度为100";
          }
          return null;
        },
        title: "请输入规则文件名",
      });

      if (!fileName) {
        return;
      }

      const kwaipilotDir = path.join(rootPath, ".kwaipilot");
      const rulesDir = path.join(kwaipilotDir, "rules");
      const ruleFilePath = path.join(rulesDir, `${fileName}${KWAIPILOT_RULES_EXT}`);

      const kwaipilotDirUri = vscode.Uri.file(kwaipilotDir);
      const rulesDirUri = vscode.Uri.file(rulesDir);
      const fileUri = vscode.Uri.file(ruleFilePath);

      try {
        await vscode.workspace.fs.stat(fileUri);
        const document = await vscode.workspace.openTextDocument(fileUri);
        await vscode.window.showTextDocument(document);
      }
      catch (err) {
        let dirCreated = false;

        try {
          await vscode.workspace.fs.stat(kwaipilotDirUri);
        }
        catch {
          // 目录不存在，创建目录
          await vscode.workspace.fs.createDirectory(kwaipilotDirUri);
          dirCreated = true;
        }

        try {
          await vscode.workspace.fs.stat(rulesDirUri);
        }
        catch {
          // 目录不存在，创建目录
          await vscode.workspace.fs.createDirectory(rulesDirUri);
          dirCreated = true;
        }

        // 如果创建了目录，初始化文件监听器
        if (dirCreated) {
          this.createFileWatcher(rulesDirUri);
        }

        const content = `---
alwaysApply: false
notes: |
  若在每次对话时均需要触发此规则，请将上方的"alwaysApply: false"中的false改为true。
  如本规则不需要跟随项目进行commit，请在.gitignore文件中添加该规则文件。
  查看[规则配置说明](https://docs.corp.kuaishou.com/d/home/<USER>
---
`;
        const contentBuffer = Buffer.from(content, "utf8");

        // 写入文件
        await vscode.workspace.fs.writeFile(fileUri, contentBuffer);
        // vscode.window.showInformationMessage(`已创建项目规则文件: ${fileName}.mdr`);

        // 打开新创建的文件
        const document = await vscode.workspace.openTextDocument(fileUri);
        await vscode.window.showTextDocument(document);
      }
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`打开项目规则文件失败: ${error.message || error}`);
    }
    finally {
      this.weblogger.$reportUserAction<"rule_setting">({
        key: "rule_setting",
        type: "project_rule",
      });
    }
  }

  /**
     * 透传webview消息到local-service
     * @param messageType 消息类型
     * @param data 消息数据
     * @returns 返回local-service的处理结果
     */
  async $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]> {
    try {
      // 使用request方法发送请求并等待响应
      const result = await this.localService.request(messageType, data);
      return result;
    }
    catch (error: any) {
      vscode.window.showErrorMessage(`透传消息到local-service失败: ${error.message || error}`);
      throw error;
    }
  }

  /**
   * 递归收集规则文件
   * @param dirUri 目录URI
   * @param relativePath 相对路径
   * @param result 结果数组
   */
  private async collectRuleFiles(
    dirUri: vscode.Uri,
    relativePath: string,
    result: string[],
  ): Promise<void> {
    if (result.length >= 1000) {
      return; // 已达到最大限制
    }

    try {
      const entries = await vscode.workspace.fs.readDirectory(dirUri);

      for (const [name, type] of entries) {
        if (result.length >= 1000) {
          break; // 已达到最大限制
        }

        const entryPath = path.join(dirUri.fsPath, name);
        const entryUri = vscode.Uri.file(entryPath);
        const entryRelativePath = `${relativePath}/${name}`;

        if (type === vscode.FileType.Directory) {
          // 递归处理子目录
          await this.collectRuleFiles(entryUri, entryRelativePath, result);
        }
        else if (type === vscode.FileType.File && isRuleFile(name)) {
          // 添加规则文件
          result.push(entryRelativePath);
        }
      }
    }
    catch (error) {
      // 忽略读取目录错误
    }
  }

  private get localService() {
    return this.ext.getCore(LocalService);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }
}

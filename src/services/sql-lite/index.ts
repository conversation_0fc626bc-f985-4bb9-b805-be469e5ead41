import { homedir } from "os";
import { ServiceModule } from "..";
import fs from "fs";
import { dirname, join } from "path";
import { ContextManager } from "../../base/context-manager";
import { Webview } from "@webview";
import { BriefSessionItem, QAItem, SessionItem } from "../../shared/types/chatHistory";
import { GlobalStateManager } from "../../base/state-manager";
import { LoggerManager } from "../../base/logger";
import { Sequelize, Model, DataTypes, Op } from "sequelize";
import * as vscode from "vscode";
import { Bridge } from "@bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { KwaipilotKV } from "./kwaipilotKV";
import { ComposerHistoryStorageService } from "../composer/ComposerHistoryStorageService";
import { ComposerSessionStorageService } from "../composer/ComposerSessionStorageService";
import { ApiConversationHistoryStorageService } from "../composer/ApiConversationHistoryStorageService";
import { BlockCodeCacheStorageService } from "../composer/BlockCodeCacheStorageService";

/**
 * catch JSON.parse 的报错, 兜底返回 null
 * @param text
 * @param reviver
 * @returns
 */
const safeJSONParse: typeof JSON.parse = (text, reviver) => {
  try {
    return JSON.parse(text, reviver);
  }
  catch {
    return null;
  }
};

export class SqlLite extends ServiceModule {
  private readonly dbPath: string = join(homedir(), ".kwaipilot", "data", "vscode", "session_data.sqlite");
  private readonly dataVersion = "v0";
  private readonly loggerScope = "sqlite";
  private sequelize?: Sequelize;

  public kwaipilotKV: KwaipilotKV | undefined = undefined;

  public initiation: Promise<void>;

  constructor(ext: ContextManager) {
    super(ext);
    const dbDir = dirname(this.dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    this.initiation = this.initSqlite();
  }

  private async initSqlite() {
    try {
      const sqlite3 = (await import("sqlite3")).default;
      const sequelize = this.sequelize = new Sequelize({
        dialect: "sqlite",
        dialectModule: sqlite3,
        storage: this.dbPath,
        // logging: true,
      });
      this.kwaipilotKV = new KwaipilotKV(sequelize);
      this.syncModels();
      this.registerListener();
    }
    catch (error) {
      this.logger.error("sqlite init failed", this.loggerScope, {
        err: error,
      });
      console.error(error);
      vscode.window.showInformationMessage("sqlite运行失败，本次历史记录将不会被持久化！");
    }
  }

  private registerListener() {
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.GET_SESSION, async (data: any) => {
      if (data.sessionId) {
        return await this.getSessionInfo(data.sessionId);
      }
      return {
        sessionList: await this.getSessionList(data),
      };
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.ADD_SESSION, (data: any) => {
      return this.addSession(data);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.ADD_MESSAGE, (data: any) => {
      return this.addMessage(data);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.UPDATE_SESSION, (data: any) => {
      return this.updateSessionInfo(data);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.DELETE_SESSION, (data) => {
      if (!data) {
        return;
      }
      return this.deleteSessionAndMessage(data.sessionId);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.CLEAR_SESSION, () => {
      return this.clearSessions();
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_UPDATE_SESSION_NAME, (data) => {
      if (!data) {
        return;
      }
      return this.updateComposerSessionName(data);
    });
  }

  private async updateComposerSessionName(data: { sessionId: string; name: string }) {
    const { sessionId, name } = data;
    const composerHistoryStorageService = this.getService(ComposerHistoryStorageService);
    await composerHistoryStorageService.patchUpdateById(sessionId, {
      name,
    });
  }

  private async deleteSessionAndMessage(sessionId: string) {
    try {
      await this.deleteSession(sessionId);
      await this.deleteMessage(sessionId);
      await this.getService(ComposerHistoryStorageService).deleteHistoryItem(sessionId);
      await this.getService(ComposerSessionStorageService).deleteSession(sessionId);
      await this.getService(ApiConversationHistoryStorageService).deleteApiConversationHistory(sessionId);
      // 清理 BlockCode 缓存
      await this.getService(BlockCodeCacheStorageService).clearSessionCache(sessionId);
    }
    catch (e) {
      this.logger.error("删除会话出错", this.loggerScope, { err: e });
    }
  }

  private async clearSessions() {
    try {
      await this.deleteSession();
      await this.deleteMessage();
      await this.getService(ComposerHistoryStorageService).clear();
      // 清空所有 BlockCode 缓存
      await this.getService(BlockCodeCacheStorageService).clearAllCache();
    }
    catch (e) {
      this.logger.error("清空会话出错", this.loggerScope, { err: e });
    }
  }

  private async updateSessionInfo(data: {
    sessionId: string;
    sessionName?: string;
    clearContextIndex?: number[];
    expiredIndex?: number[];
    extra?: SessionExtra;
  }) {
    const { sessionId, sessionName, clearContextIndex, expiredIndex, extra } = data;
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return;
      }
      if (sessionName) {
        await session.update({ session_name: sessionName });
      }
      if (clearContextIndex) {
        await session.update({ clear_context_index: clearContextIndex });
      }
      if (expiredIndex) {
        await session.update({ expired_index: expiredIndex });
      }
      if (extra) {
        await session.update({ extra });
      }
    }
    catch (e) {
      this.logger.error("更新会话信息出错", this.loggerScope, { err: e });
    }
  }

  private async addSession(data: SessionItem) {
    const { sessionId, sessionName } = data;
    const now = Date.now();
    try {
      await Session.create({
        session_id: sessionId,
        session_name: sessionName,
        expired_index: [],
        clear_context_index: [],
        create_time: now,
        update_time: now,
        extra: buildSessionExtra({ ...data, workspaceUri: this.context.storageUri?.toString() || "" }),
      });
    }
    catch (e) {
      this.logger.error("添加会话出错", this.loggerScope, { err: e });
    }
  }

  private async addMessage(data: { sessionId: string; chatId: string; item: QAItem }) {
    const { sessionId, chatId, item } = data;
    const now = Date.now();
    try {
      const session = await this.getSession(sessionId);
      if (session) {
        await session.update({ update_time: now });
      }
      const message = await this.getMessage(sessionId, chatId);
      if (!message) {
        await Message.create({
          session_id: sessionId,
          chat_id: chatId,
          content: item,
          create_time: now,
          update_time: now,
        });
      }
      else {
        await message.update({ content: item, update_time: now });
      }
    }
    catch (error) {
      this.logger.error("添加或更新消息出错", this.loggerScope, {
        err: error,
      });
    }
  }

  private async getSessionList(data: { page: number; pageSize: number; timeRange: string }): Promise<BriefSessionItem[]> {
    const { page = 1, pageSize = 50, timeRange = "all" } = data;
    const offset = (page - 1) * pageSize;
    let whereCondition = {};

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    switch (timeRange) {
      case "today":
        whereCondition = { update_time: { [Op.gte]: today } };
        break;
      case "yesterday":
        whereCondition = { update_time: { [Op.gte]: yesterday.getTime(), [Op.lt]: today } };
        break;
      case "sevenDays":
        whereCondition = { update_time: { [Op.gte]: sevenDaysAgo.getTime(), [Op.lt]: yesterday.getTime() } };
        break;
      case "earlier":
        whereCondition = { update_time: { [Op.lt]: sevenDaysAgo.getTime() } };
        break;
      default:
        // 不设置时间范围条件
        break;
    }

    try {
      const res = await Session.findAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [["update_time", "DESC"]],
      });
      return res.map(r => ({
        sessionId: r.session_id,
        sessionName: r.session_name,
        sessionTime: this.timestampToString(r.update_time),
        isComposer: Boolean(r.extra.isComposer),
        isComposerV2: false,
        workspaceUri: r.extra.workspaceUri ?? "",
      }));
    }
    catch (error) {
      this.logger.error("获取目标会话时出错", this.loggerScope, { err: error });
      return [];
    }
  }

  private async getSessionInfo(sessionId: string): Promise<SessionItem | undefined> {
    const session = await Session.findOne({
      where: {
        session_id: sessionId,
      },
    });
    if (!session) {
      return undefined;
    }
    const messages = await Message.findAll({
      where: {
        session_id: sessionId,
      },
      order: ["create_time"],
    });
    return {
      sessionId,
      sessionName: session.session_name,
      isComposer: Boolean(session.extra.isComposer),
      sessionTime: this.timestampToString(session.update_time),
      expiredIndex: session.expired_index,
      clearContextIndex: session.clear_context_index,
      cachedMessages: messages.map(message => message.content),
    };
  };

  private async getSession(sessionId: string) {
    return await Session.findOne({
      where: {
        session_id: sessionId,
      },
    });
  }

  private async getMessage(sessionId: string, chatId: string) {
    return await Message.findOne({
      where: {
        session_id: sessionId,
        chat_id: chatId,
      },
    });
  }

  private async deleteSession(sessionId?: string) {
    if (sessionId) {
      await Session.destroy({
        where: {
          session_id: sessionId,
        },
      });
    }
    else {
      await Session.destroy({
        truncate: true,
      });
    }
  }

  private async deleteMessage(sessionId?: string) {
    if (sessionId) {
      await Message.destroy({
        where: {
          session_id: sessionId,
        },
      });
    }
    else {
      await Message.destroy({
        truncate: true,
      });
    }
  }

  private async syncModels() {
    Message.init({
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      session_id: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      chat_id: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
        get() {
          const rawValue = this.getDataValue("content");
          return JSON.parse(rawValue);
        },
        set(value: QAItem) {
          this.setDataValue("content", JSON.stringify(value));
        },
      },
      create_time: {
        type: DataTypes.BIGINT,
        allowNull: false,
      },
      update_time: {
        type: DataTypes.BIGINT,
        allowNull: false,
      },
      data_version: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: this.dataVersion,
      },
      extra: {
        type: DataTypes.STRING,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue("extra");
          return JSON.parse(rawValue);
        },
        set(value: MessageExtra) {
          this.setDataValue("extra", JSON.stringify(value));
        },
      },
    }, {
      sequelize: this.sequelize!,
      tableName: "message",
      timestamps: false,
      indexes: [
        {
          fields: ["session_id"],
          name: "idx_session_id_message",
        },
      ],
    });
    Session.init({
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      session_id: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      session_name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      expired_index: {
        type: DataTypes.TEXT,
        allowNull: false,
        get() {
          const rawValue = this.getDataValue("expired_index");
          return JSON.parse(rawValue);
        },
        set(value: number[]) {
          this.setDataValue("expired_index", JSON.stringify(value));
        },
      },
      clear_context_index: {
        type: DataTypes.TEXT,
        allowNull: false,
        get() {
          const rawValue = this.getDataValue("clear_context_index");
          return JSON.parse(rawValue);
        },
        set(value: number[]) {
          this.setDataValue("clear_context_index", JSON.stringify(value));
        },
      },
      create_time: {
        type: DataTypes.BIGINT,
        allowNull: false,
      },
      update_time: {
        type: DataTypes.BIGINT,
        allowNull: false,
      },
      extra: {
        type: DataTypes.STRING,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue("extra");
          return safeJSONParse(rawValue) || {};
        },
        set(value: SessionExtra) {
          this.setDataValue("extra", JSON.stringify(value));
        },
      },
    }, {
      sequelize: this.sequelize!,
      tableName: "session",
      timestamps: false,
      indexes: [
        {
          fields: ["session_id"],
          name: "idx_session_id_session",
        },
      ],
    });

    await this.sequelize?.sync();
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }

  private timestampToString(timestamps: number) {
    return new Date(timestamps)
      .toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      })
      .replace(/\//g, "/");
  }
}

// 定义 Message 模型
class Message extends Model {
  public id!: number;
  public session_id!: string;
  public chat_id!: string;
  public content!: QAItem;
  public create_time!: number;
  public update_time!: number;
  public date_version!: string;
  public extra!: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface MessageExtra {

}

class Session extends Model {
  public id!: number;
  public session_id!: string;
  public session_name!: string;
  public expired_index!: number[];
  public clear_context_index!: number[];
  public create_time!: number;
  public update_time!: number;
  public extra!: SessionExtra;
}

export interface SessionExtra {
  isComposer: boolean;
  /** 对话所属的工作区 uri，用于区分不同工作区的对话, 如果用户没有打开工作区，则为无归属的对话，使用空字符串表示 */
  workspaceUri: string;
}

function buildSessionExtra(sessionItem: SessionItem & { workspaceUri: string }): Required<SessionExtra> {
  return {
    isComposer: sessionItem.isComposer ?? false,
    workspaceUri: sessionItem.workspaceUri ?? "",
  };
}

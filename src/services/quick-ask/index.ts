import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { Webview } from "@webview";
import { WebloggerManager } from "../../base/weblogger";
import fs from "fs";
import { File } from "../../core/file";
import { CodeActionParams } from "../../shared/types";
import { Bridge } from "@bridge";
import { WEBVIEW_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
/**
 * 遍历文件/文件夹
 * @todo 迁移至 base/fileSystem
 * @param path
 * @returns
 */
function walkFileOrDirectory(path: string) {
  let files: string[] = [];
  if (fs.lstatSync(path).isDirectory()) {
    const fileStr = fs.readdirSync(path);
    fileStr.forEach((ff) => {
      files = files.concat(walkFileOrDirectory(`${path}/${ff}`));
    });
  }
  else {
    files = [path];
  }
  return files;
}

export class QuickAskService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.registryListener();
  }

  registryListener() {
    this.context.subscriptions.push(
      vscode.commands.registerCommand(
        "kwaipilot.quickAsk",
        (uri: vscode.Uri) => {
          this.startQuickAsk(uri);
        },
      ),
      vscode.commands.registerCommand(
        "kwaipilot.generateCommentMessage",
        () => {
          this.executeCommand("kwaipilot.generateCommentMessage");
        },
      ),
      vscode.commands.registerCommand("kwaipilot.generateUnitTest", () => {
        this.executeCommand("kwaipilot.generateUnitTest");
      }),
    );
  }

  async startQuickAsk(uri: vscode.Uri) {
    const files = await walkFileOrDirectory(uri.fsPath);
    this.weblogger.sendNodeClick("VS_QUICK_ASK");

    this.webview.focus("menu_click");
    this.getCore(File).uploadFiles(
      files.map((path: string) => vscode.Uri.file(path)),
    );
  }

  executeCommand(command: string) {
    if (!this.checkSelection()) {
      return;
    }
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    const selection = editor?.selection;
    const document = editor.document;
    const selectText = document.getText(selection);
    const languageId = document.languageId;

    let type: CodeActionParams["type"] = "comment";
    switch (command) {
      case "kwaipilot.generateCommentMessage":
        type = "comment";
        break;
      case "kwaipilot.generateUnitTest":
        type = "test";
        break;
      case "kwaipilot.explain":
        type = "explain";
    }
    this.webview.focus("menu_click");
    const sidePanelWebview = this.getBase(Webview)._view?.webview;
    if (!sidePanelWebview) {
      throw new Error("side panel webview is not ready");
    }
    this.getBase(Bridge).callHandler(sidePanelWebview, WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE, {
      type,
      selectText,
      languageId,
      uri: document.uri.toString(),
      relativePath: vscode.workspace.asRelativePath(document.uri),
      section: {
        lineStart: selection.start.line,
        lineEnd: selection.end.line,
        content: selectText,
        type: "selection",
        name: "",
        kind: "",
        offsetStart: selection.start.character,
        offsetEnd: selection.end.character,
        blockStart: 0,
        blockEnd: 0,
      },
    });
  }

  private checkSelection() {
    const selection = vscode.window.activeTextEditor?.selection;
    const selectedText = vscode.window.activeTextEditor?.document.getText(
      selection,
    );
    if (!selectedText) {
      vscode.window.showErrorMessage("请选中一段代码！");
      return false;
    }
    return true;
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }
}

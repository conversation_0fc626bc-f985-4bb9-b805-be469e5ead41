import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { LoggerManager } from "../../base/logger";
import { AgentModule } from "../agent";
import { ConfigManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, StateReturnType, WorkspaceState } from "../../base/state-manager/types";
import { getNonce } from "../../utils/getNonce";
import { Bridge } from "@bridge";
import { createExtensionRpcContext } from "../../base/bridge/ExtensionRpcContext";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { ExtensionSettingsShape } from "shared/lib/bridge/protocol";
import { SettingPage } from "shared/lib/customSettingPanel";
import { upload } from "../../api/upload";
import { LOG_PATH } from "../../common/const";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { exec } from "child_process";

export class SettingPanelModule extends CoreModule implements ExtensionSettingsShape {
  private readonly loggerScope = "SettingPanelModule";
  private panel: vscode.WebviewPanel | undefined;
  rpcContext: IRPCProtocol;
  private isInIDE = false;

  /**
   * 获取设置面板的webview
   * @returns 设置面板的webview实例，如果不存在则返回undefined
   */
  public getPanelWebview() {
    return this.panel?.webview;
  }

  constructor(readonly ext: ContextManager) {
    super(ext);
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.openBasicsManagement", (args) => {
        this.isInIDE = args?.isInIDE ?? false;
        this.openCustomSettingPanel("basics");
      }),
      vscode.commands.registerCommand("kwaipilot.openFunctionManagement", () => {
        this.openCustomSettingPanel("function");
      }),
      vscode.commands.registerCommand("kwaipilot.openCodeIndexManagement", () => {
        this.openCustomSettingPanel("fileIndex");
      }),
      vscode.commands.registerCommand("kwaipilot.openRulesManagement", () => {
        this.openCustomSettingPanel("rules");
      }),
      vscode.commands.registerCommand("kwaipilot.openMCPManagement", (query) => {
        this.openCustomSettingPanel("mcp", query);
      }),
    );
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE, (data, source) => {
            if (source !== this.panel?.webview) {
              return;
            }
            listener(data);
          });
        },
        send: (message) => {
          const webview = this.panel?.webview;
          if (!webview) {
            throw new Error("no webview");
          }
          this.getBase(Bridge).postOneWayMessage(webview, WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE, message);
        },
      },
    });
  }

  $getSettings(): StateReturnType["config"] {
    return {
      proxy: this.config.get(Config.PROXY_URL),
      [Config.MODEL_TYPE]: this.config.get(Config.MODEL_TYPE),
      [Config.ENABLE]: this.config.get(Config.ENABLE),
      [Config.COMMENT_COMPLETION_ENABLE]: this.config.get(Config.COMMENT_COMPLETION_ENABLE),
      [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: this.config.get(Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION),
      [Config.CODE_COMPLETION_DELAY]: this.config.get(Config.CODE_COMPLETION_DELAY),
      [Config.ENABLE_CODE_BLOCK_ACTION]: this.config.get(Config.ENABLE_CODE_BLOCK_ACTION),
      [Config.PREDICTION_ENABLE]: this.config.get(Config.PREDICTION_ENABLE),
      [Config.ENABLE_LOCAL_AGENT]: this.config.get(Config.ENABLE_LOCAL_AGENT),
      [Config.AGENT_PREFERENCE]: this.config.get(Config.AGENT_PREFERENCE),
      [Config.ENABLE_DIAGNOSTICS_CHECK]: this.config.get(Config.ENABLE_DIAGNOSTICS_CHECK),
      [Config.COMPOSER_ENABLE_AUTO_RUN]: this.config.get(Config.COMPOSER_ENABLE_AUTO_RUN),
      [Config.COMPOSER_ENABLE_AUTO_RUN_MCP]: this.config.get(Config.COMPOSER_ENABLE_AUTO_RUN_MCP),
      [Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE]: this.config.get(Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE),
    };
  }

  $updateSetting(key: keyof StateReturnType["config"], value: StateReturnType["config"][typeof key]): void {
    this.config.update(key, value);
  }

  $openSettings(activePage?: SettingPage, query?: string) {
    this.openCustomSettingPanel(activePage, query);
  }

  async $generateLogUrl(): Promise<void> {
    // 生成压缩包文件名，包含时间戳
    const timestamp = new Date().toISOString().replace(/[-:]/g, "").replace(/\..+/, "").replace("T", "_");
    const tempDir = os.tmpdir();
    const tarFileName = `logs_backup_${timestamp}.tar.gz`;
    const tarFilePath = path.join(tempDir, tarFileName);

    try {
      // 检查日志目录是否存在
      if (!fs.existsSync(LOG_PATH)) {
        this.logger.warn("日志目录不存在", "generateLogUrl", { value: { path: LOG_PATH } });
        vscode.window.showWarningMessage("日志目录不存在，无法生成日志包");
        return;
      }

      // 显示进度提示
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "正在生成日志包...",
        cancellable: false,
      }, async (progress) => {
        progress.report({ increment: 0, message: "正在筛选日志文件..." });

        // 先筛选出需要打包的文件，避免 tar 处理大量排除规则
        const logFiles: string[] = [];
        try {
          const files = fs.readdirSync(LOG_PATH);
          for (const file of files) {
            const filePath = path.join(LOG_PATH, file);
            const stat = fs.statSync(filePath);

            // 跳过目录，只处理文件
            if (!stat.isFile()) {
              continue;
            }

            // 只包含最近的日志文件（没有日期后缀的当前日志文件，以及小于 50MB 的日志文件）
            if (!file.endsWith(".gz")
              && !file.match(/.*-\d{8}-.*/)
              && !file.match(/.*_\d{8}_.*/)
              && !file.match(/.*\.\d{4}-\d{2}-\d{2}.*/)
              && !file.match(/^\d{8}-.*/)
              && file.match(/\.(log|txt)$|^(mcp|vscode|kwaipilot).*$/)
              && stat.size < 50 * 1024 * 1024) { // 限制单个文件大小不超过 50MB
              logFiles.push(file);
            }
          }
          this.logger.info("筛选出的日志文件", "generateLogUrl", {
            value: { files: logFiles, totalFiles: files.length },
          });
        }
        catch (error) {
          this.logger.error("读取日志目录失败", "generateLogUrl", { err: error });
          throw new Error("读取日志目录失败");
        }

        if (logFiles.length === 0) {
          vscode.window.showWarningMessage("没有找到需要打包的日志文件");
          return;
        }

        progress.report({ increment: 30, message: "正在压缩日志文件..." });

        // 使用筛选出的文件列表进行压缩，避免压缩不需要的文件
        const fileList = logFiles.map(file => `"${file}"`).join(" ");
        // 在日志目录内执行压缩，只压缩筛选出的文件
        const tarCommand = `cd "${LOG_PATH}" && tar -czf "${tarFilePath}" ${fileList}`;

        this.logger.info("开始压缩日志文件", "generateLogUrl", {
          value: {
            command: tarCommand,
            logPath: LOG_PATH,
            tarFile: tarFilePath,
            fileCount: logFiles.length,
            files: logFiles,
          },
        });

        // 添加超时机制
        const compressionPromise = new Promise<void>((resolve, reject) => {
          const process = exec(tarCommand, { timeout: 60000 }, (error: any, stdout: string, stderr: string) => {
            if (error) {
              if (error.code === "ETIMEDOUT") {
                this.logger.error("压缩超时", "generateLogUrl", { err: error });
                reject(new Error("压缩操作超时，请检查日志文件大小"));
              }
              else {
                this.logger.error("压缩日志失败", "generateLogUrl", { err: error, value: { stderr } });
                reject(error);
              }
              return;
            }
            this.logger.info("日志压缩完成", "generateLogUrl", { value: { stdout, stderr } });
            resolve();
          });

          // 手动超时处理
          const timeoutId = setTimeout(() => {
            process.kill();
            reject(new Error("压缩操作超时（60秒），请检查日志文件大小"));
          }, 60000);

          process.on("exit", () => {
            clearTimeout(timeoutId);
          });
        });

        await compressionPromise;

        progress.report({ increment: 40, message: "正在验证压缩包..." });
        // 检查压缩包是否创建成功
        if (!fs.existsSync(tarFilePath)) {
          throw new Error("压缩包创建失败");
        }

        // 检查压缩包大小
        const stats = fs.statSync(tarFilePath);
        this.logger.info("压缩包创建成功", "generateLogUrl", {
          value: { tarFile: tarFilePath, size: stats.size },
        });

        progress.report({ increment: 20, message: "压缩完成，准备上传..." });

        // 获取代理 URL
        const proxyUrl = this.config.get(Config.PROXY_URL);

        // 上传压缩包
        // 检查压缩包大小，如果太大则提示用户
        const maxUploadSize = 100 * 1024 * 1024; // 100MB
        if (stats.size > maxUploadSize) {
          throw new Error(`压缩包过大 (${Math.round(stats.size / 1024 / 1024)}MB)，请联系管理员`);
        }

        this.logger.info("开始上传日志压缩包", "generateLogUrl", {
          value: {
            tarFile: tarFilePath,
            proxyUrl,
            fileSize: stats.size,
            fileSizeMB: Math.round(stats.size / 1024 / 1024),
          },
        });

        const uploadResult = await new Promise<any>((resolve, reject) => {
          // 添加上传超时
          const uploadTimeout = setTimeout(() => {
            reject(new Error("上传超时（5分钟），请检查网络连接或文件大小"));
          }, 5 * 60 * 1000); // 5分钟上传超时

          upload(proxyUrl, {
            file: vscode.Uri.file(tarFilePath),
            onStart: (file) => {
              this.logger.info("开始上传", "generateLogUrl", {
                value: {
                  file: file.filename,
                  size: stats.size,
                },
              });
            },
            onProgress: (file) => {
              this.logger.info("上传进度", "generateLogUrl", {
                value: {
                  progress: file.progress,
                  filename: file.filename,
                },
              });
            },
            onSuccess: (data) => {
              clearTimeout(uploadTimeout);
              this.logger.info("上传成功", "generateLogUrl", { value: data });
              resolve(data);
            },
            onFailed: (file) => {
              clearTimeout(uploadTimeout);
              this.logger.error("上传失败", "generateLogUrl", { value: file });
              reject(new Error(`上传失败: ${file.filename}${file.error ? ` - ${file.error}` : ""}`));
            },
          });
        });

        // 复制 URL 到剪贴板并提示用户
        if (uploadResult && uploadResult.url) {
          await vscode.env.clipboard.writeText(uploadResult.url);
          this.logger.info("日志 URL 生成成功", "generateLogUrl", { value: { url: uploadResult.url } });
          vscode.window.showInformationMessage(`日志已成功上传，URL 已复制到剪贴板: ${uploadResult.url}`);
        }
        else {
          throw new Error("上传成功但未返回 URL");
        }
      });
    }
    catch (error: any) {
      this.logger.error("生成日志 URL 失败", "generateLogUrl", {
        err: error,
        value: {
          message: error.message,
          stack: error.stack,
          code: error.code,
        },
      });

      // 根据错误类型提供更具体的错误信息
      let errorMessage = `生成日志 URL 失败: ${error.message}`;
      if (error.message.includes("压缩")) {
        errorMessage += "\n提示：可能是日志文件过大或磁盘空间不足";
      }
      else if (error.message.includes("上传")) {
        errorMessage += "\n提示：请检查网络连接和代理设置";
      }
      else if (error.message.includes("超时")) {
        errorMessage += "\n提示：操作超时，请稍后重试";
      }

      vscode.window.showErrorMessage(errorMessage);
    }
    finally {
      // 无论成功还是失败，都删除本地压缩包
      if (fs.existsSync(tarFilePath)) {
        try {
          fs.unlinkSync(tarFilePath);
          this.logger.info("已删除本地压缩包", "generateLogUrl", { value: { tarFile: tarFilePath } });
        }
        catch (deleteError) {
          this.logger.warn("删除本地压缩包失败", "generateLogUrl", {
            value: {
              err: deleteError,
              tarFile: tarFilePath,
            },
          });
        }
      }
    }
  }

  openCustomSettingPanel(activePage: SettingPage = "fileIndex", query?: string) {
    this.workspaceState.update(WorkspaceState.ACTIVE_SETTING_PAGE, activePage);
    if (this.panel) {
      this.panel.webview.html = this._getHtmlForWebview(this.panel.webview, this.context.extensionUri, query);
      this.panel.reveal();
      return;
    }
    this.panel = vscode.window.createWebviewPanel(
      "kwaipilot-setting-panel",
      "Kwaipilot 设置",
      {
        viewColumn: vscode.ViewColumn.Active,
        preserveFocus: true,
      },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
      },
    );

    // 设置图标
    const iconPath = {
      light: vscode.Uri.joinPath(this.context.extensionUri, "resources", "light", "settings-gear.svg"),
      dark: vscode.Uri.joinPath(this.context.extensionUri, "resources", "dark", "settings-gear.svg"),
    };
    this.panel.iconPath = iconPath;

    // 监听面板关闭事件
    this.panel.onDidDispose(
      () => {
        this.panel = undefined;
      },
      null,
      this.context.subscriptions,
    );

    const webview = this.panel.webview;
    // 监听 WebView 消息
    webview.onDidReceiveMessage((message) => {
      if (message.protocol === "callHandler") {
        this.getBase(Bridge).callNativeHandler(
          webview,
          message.name,
          message.data,
          message.callbackId,
        );
      }
      else if (message.protocol === "callback") {
        this.getBase(Bridge).handleCallback(message.callbackId, message.data);
      }
      else if (message.protocol === "message") {
        this.getBase(Bridge).handleOneWayMessage(webview, message.data);
      }
    });
    // 设置 WebView 内容
    this.panel.webview.html = this._getHtmlForWebview(this.panel.webview, this.context.extensionUri, query);
  }

  $login(host?: "ide" | "plugin") {
    let cmd = "kwaipilot.login";
    if (host === "ide") {
      cmd = "kwaipilot.loginIDE";
    }
    return vscode.commands.executeCommand(cmd);
  }

  $logout(host?: "ide" | "plugin") {
    let cmd = "kwaipilot.logout";
    if (host === "ide") {
      cmd = "kwaipilot.logoutIDE";
    }
    return vscode.commands.executeCommand(cmd);
  }

  /**
   *【调用须知】 这个方法只有在 Kwaipilot IDE 里会提供，其他 IDE 不提供
   * @param productName
   */
  $importSettings(productName: string) {
    // 必须返回，因为需要 await 效果
    return vscode.commands.executeCommand("kwaipilot.importSettings", productName, false);
  }

  $openIdeShortcutSettings() {
    vscode.commands.executeCommand("workbench.action.openGlobalKeybindings");
  }

  $openIdeUserSettings() {
    vscode.commands.executeCommand("workbench.action.openSettings");
  }

  /**
   * 获取webView视图
   * @param webview webView组件
   * @returns
   */
  private _getHtmlForWebview(
    webview: vscode.Webview,
    extensionUri: vscode.Uri,
    query?: string,
  ) {
    const inDevelopmentMode = this.context.extensionMode === vscode.ExtensionMode.Development;
    const vscMediaUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "setting-ui/assets"))
      .toString();

    const jsUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "bridge/index.js"))
      .toString();

    let scriptUri: string;
    let styleMainUri: string;
    if (!inDevelopmentMode) {
      scriptUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "setting-ui/build/assets/index.js"),
        )
        .toString();
      styleMainUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "setting-ui/build/assets/index.css"),
        )
        .toString();
    }
    else {
      scriptUri = "http://localhost:5174/src/index.tsx";
      styleMainUri = "http://localhost:5174/src/App.css";
    }
    this.logger.info("init webview", "webview", {
      value: {
        scriptUri,
        styleMainUri,
        vscMediaUrl,
      },
    });
    const nonce = getNonce();
    const currentTheme = vscode.window.activeColorTheme;
    const isLight = currentTheme?.kind === 1 || currentTheme?.kind === 4;
    const proxyUrl = this.getBase(ConfigManager).get(Config.PROXY_URL);
    return `<!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="${styleMainUri}" rel="stylesheet">
          <script>window.vscMediaUrl = "${vscMediaUrl}"</script>
          <script>window.proxyUrl = "${proxyUrl}"</script>
          <script>window.ide = "vscode"</script>
          <script>window.colorThemeName = "dark"</script>
          <script>window.uriQuery = "${query}"</script>
          <script nonce="${nonce}" src="${jsUrl}"></script>
          <script>window.__KWAIPILOT_ENV__IN_IDE__ = ${this.isInIDE}</script>
          <title>Continue</title>
        </head>
        <body ${!isLight ? "class='dark'" : ""}>
          <div id="root"></div>
          ${inDevelopmentMode
              ? `<script type="module">
            import RefreshRuntime from "http://localhost:5174/@react-refresh"
            RefreshRuntime.injectIntoGlobalHook(window)
            window.$RefreshReg$ = () => {}
            window.$RefreshSig$ = () => (type) => type
            window.__vite_plugin_react_preamble_installed__ = true
            </script>
            <script type="module">
              import { createHotContext } from "http://localhost:5174/@vite/client"
              window.__vite_hot_context__ = createHotContext()
            </script>`
              : ""
          }
          <script type="module" nonce="${nonce}" src="${scriptUri}"></script>
        </body>
      </html>`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get agent() {
    return this.getCore(AgentModule);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }
}

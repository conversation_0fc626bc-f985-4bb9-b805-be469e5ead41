import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { LoggerManager } from "../../base/logger";

import { Bridge } from "@bridge";
import { Project } from "../project";

import * as vscode from "vscode";
import { version } from "../../../package.json";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { SettingPanelModule } from "../setting-panel";
import { ConfigManager, GlobalStateManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, GlobalState, WorkspaceState } from "../../base/state-manager/types";
import { Api } from "../../base/http-client";
import { LocalService } from "../localService";
import { DefaultBaseUrl, KwaipilotEnv } from "../../const";
import { IndexFileService } from "../../services/index-file";
import { IndexState } from "shared";

export class AgentModule extends CoreModule {
  private readonly loggerScope = "AgentModule";

  constructor(readonly ext: ContextManager) {
    super(ext);
    this.initAgent();
  }

  async onLocalServiceConnected() {
    this.registerHandlers();

    const indexState: IndexState = {
      ...this.workspaceState.get(WorkspaceState.INDEX_STATE, {
        indexed: false,
        indexing: false,
        indexingProgress: 0,
        indexingMessage: "",
        lastBuildTime: "",
        pauseIndexManual: false,
        status: "paused",
      }),
    };
    const res = await this.messenger.request("state/checkRepoState", undefined);
    const shouldBuildIndex = (this.configManager.get(Config.ENABLE_LOCAL_AGENT) ?? true) && (!res.data?.isPaused) && !!this.project.getRepoPath();
    if (shouldBuildIndex) {
      if (res.status === "ok" && res.data?.commitId === this.project.getCurrentCommit() && res.data?.progress === 1) {
        this.indexFileService.updateProgress({
          progress: 1,
        });
        this.messenger.sendMessage("index/build", undefined);
        return;
      }
      else {
        this.messenger.sendMessage("index/repoIndex", undefined);
        indexState.indexing = true;
        indexState.pauseIndexManual = false;
        indexState.status = "indexing";
        await this.workspaceState.update(WorkspaceState.INDEX_STATE, indexState);
      }
    }
    else {
      indexState.indexing = false;
      indexState.indexingProgress = 0;
      indexState.indexingMessage = "";
      indexState.lastBuildTime = "";
      indexState.pauseIndexManual = false;
      // 非git仓库，indexed为true，不用提示用户去构建仓库
      indexState.indexed = true;
      indexState.status = "indexed";
      await this.workspaceState.update(WorkspaceState.INDEX_STATE, indexState);
    }
  }

  private async initAgent() {
    try {
      const userInfo = this.getBase(GlobalStateManager).get(GlobalState.USER_INFO);
      if (!userInfo?.name) {
        this.logger.warn("User not logged in, skipping agent initialization", this.loggerScope);
        return;
      }

      const grayResponse = await this.httpClient.checkAgentGray(userInfo.name);
      if (grayResponse.status === 200 && grayResponse.data.enableLocalAgent) {
        // this.configManager.update(Config.ENABLE_LOCAL_AGENT, true);
      }
      else {
        // this.configManager.update(Config.ENABLE_LOCAL_AGENT, false);
        this.logger.info("User not in agent gray list", this.loggerScope);
      }
    }
    catch (error) {
      this.logger.error("Failed to check agent gray status:", this.loggerScope, { err: error });
      // this.configManager.update(Config.ENABLE_LOCAL_AGENT, false);
    }
  }

  registerHandlers() {
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.OPEN_SETTING, () => {
      this.settingPanel.openCustomSettingPanel();
    });
    vscode.workspace.onDidSaveTextDocument((document) => {
      this.messenger.sendMessage("index/file", {
        file: vscode.workspace.asRelativePath(document.uri.fsPath),
        action: "modify",
      });
    });
    vscode.workspace.onDidDeleteFiles((event) => {
      event.files.forEach((file) => {
        this.messenger.sendMessage("index/file", {
          file: vscode.workspace.asRelativePath(file.fsPath),
          action: "delete",
        });
      });
    });
    vscode.workspace.onDidCreateFiles((event) => {
      event.files.forEach((file) => {
        this.messenger.sendMessage("index/file", {
          file: vscode.workspace.asRelativePath(file.fsPath),
          action: "create",
        });
      });
    });
    vscode.workspace.onDidRenameFiles((event) => {
      event.files.forEach((file) => {
        this.messenger.sendMessage("index/file", {
          file: vscode.workspace.asRelativePath(file.oldUri.fsPath),
          action: "delete",
        });
        this.messenger.sendMessage("index/file", {
          file: vscode.workspace.asRelativePath(file.newUri.fsPath),
          action: "create",
        });
      });
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.INDEX_FILE, (payload) => {
      if (!payload) {
        return;
      }
      this.messenger.sendMessage("index/file", {
        file: payload.file,
        action: payload.action,
      });
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.INDEX_CLEAR_INDEX, () => {
      this.messenger.sendMessage("index/clearIndex", undefined);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.INDEX_REPO_INDEX, () => {
      this.messenger.sendMessage("index/repoIndex", { manual: true });
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.STATE_CHECK_REPO_STATE, () => {
      this.messenger.sendMessage("state/checkRepoState", undefined);
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.SEARCH_SEARCH, async (payload) => {
      if (!payload) {
        return;
      }
      const result = await this.messenger.request("search/search", payload);
      return result;
    });
    this.messenger.onMessage("state/ideInfo", () => {
      const config = this.getBase(ConfigManager);
      const proxyUrl = config.get(Config.PROXY_URL) ?? "";
      const maxIndexSpace = this.globalState.get(GlobalState.MAX_INDEX_SPACE, 10);
      return {
        status: "ok" as const,
        data: {
          pluginVersion: version,
          platform: KwaipilotEnv.isInIde ? "kwaipilot-ide" : "vscode",
          device: KwaipilotEnv.isInIde ? "kwaipilot-ide" : "kwaipilot-vscode",
          version: KwaipilotEnv.isInIde ? vscode.appVersion : vscode.version,
          repoInfo: {
            git_url: this.project.getRemoteOriginUrl(this.project.getRepoPath() || "") || "",
            dir_path: this.project.getRepoPath() || "",
            commit: this.project.getCurrentCommit() || "",
            branch: this.project.getCurrentBranch(this.project.getRepoPath() || "") || "",
          },
          userInfo: {
            name: this.getBase(GlobalStateManager).get(GlobalState.USER_INFO)?.name || "",
          },
          proxyUrl,
          maxIndexSpace,
          cwd:
            vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) || "",
        },
      };
    });
    this.messenger.onMessage("config/getIdeSetting", () => {
      return {
        status: "ok",
        data: {
          dirPath: this.project.getRepoPath() || "",
          fileRetryTime: 3,
          modelRetryTime: 3,
          enableRepoIndex: this.configManager.get(Config.ENABLE_LOCAL_AGENT) ?? true,
          maxIndexSpace: this.globalState.get(GlobalState.MAX_INDEX_SPACE) ?? 10,
          proxyUrl: this.configManager.get(Config.PROXY_URL) ?? DefaultBaseUrl,
          agentPreference: this.configManager.get(Config.AGENT_PREFERENCE) || "intelligent",
        },
      };
    });
    this.messenger.onMessage("state/ideState", () => {
      return {
        status: "ok",
      };
    });
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }

  private get project() {
    return this.getCore(Project);
  }

  private get settingPanel() {
    return this.getCore(SettingPanelModule);
  }

  private get configManager() {
    return this.getBase(ConfigManager);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get httpClient() {
    return this.getBase(Api);
  }

  private get messenger() {
    return this.getCore(LocalService);
  }

  private get indexFileService() {
    return this.dangerouslyGetService(IndexFileService);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }
}

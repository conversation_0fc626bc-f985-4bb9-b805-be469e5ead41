import axios, { AxiosProgressEvent, CanceledError } from "axios";
import * as vscode from "vscode";
import * as fs from "fs";
import { fromFile } from "file-type";
import { v4 as uuidv4 } from "uuid";

export interface UploadParams {
  file: vscode.Uri;
  onStart?: (file: UploadFile) => void;
  onProgress?: (file: UploadFile) => void;
  onSuccess?: (data: UploadFile) => void;
  onFailed?: (file: UploadFile) => void;
}
export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}

export const upload = async (url: string, { file, onStart, onProgress, onSuccess, onFailed }: UploadParams) => {
  const fileStream = fs.readFileSync(file.path);
  // 修复跨平台路径问题
  const filename = file.path.split(/[/\\]/).pop();
  const fileType = await fromFile(file.path);

  // 为 tar.gz 文件设置正确的 MIME 类型
  let mimeType: string;
  if (fileType?.mime) {
    mimeType = fileType.mime as string;
  }
  else if (filename?.endsWith(".tar.gz")) {
    mimeType = "application/gzip";
  }
  else if (filename?.endsWith(".tar")) {
    mimeType = "application/x-tar";
  }
  else {
    mimeType = "application/octet-stream"; // 默认二进制类型
  }

  let blob;
  let formData: any;
  try {
    blob = new Blob([fileStream], { type: mimeType });
    formData = new FormData();
    formData.append("file", blob, filename);
  }
  catch (e) {
    console.log("使用 formdata-node 兼容性处理:", e);
    const { FormData, Blob } = await import("formdata-node");
    const { ReadableStream } = await import("web-streams-polyfill");
    if (!global.ReadableStream) {
      global.ReadableStream = ReadableStream as any;
    }
    blob = new Blob([fileStream], { type: mimeType });
    formData = new FormData();
    formData.append("file", blob, filename);
  }

  const fileInfo: UploadFile = {
    filename: filename || "",
    type: fileType?.mime,
    size: fileStream.length,
    uid: uuidv4(),
    status: "uploading",
    progress: 0,
  };
  onStart?.(fileInfo);
  const promise = new Promise((resolve, reject) => {
    // temp?.("取消请求"); // 取消上一次请求
    axios({
      method: "post",
      url: `${url}/eapi/kwaipilot/file/upload`,
      data: formData,
      // 针对大文件优化超时设置
      timeout: Math.max(60000, fileInfo.size / 1024), // 至少1分钟，每KB多1ms
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (e: AxiosProgressEvent) => {
        const progress = Math.round((e.loaded / (e.total || e.bytes || e.loaded)) * 100);
        onProgress?.({
          ...fileInfo,
          progress: Math.min(progress, 100),
          status: "uploading",
          loaded: e.loaded,
          total: e.total,
        });
      },
    }).then((response) => {
      if (response?.status == 200 && response?.data && response.data?.status == 200 && response.data.data) {
        // console.log('success',response.data.data);
        onSuccess?.({
          ...fileInfo,
          ...response.data.data,
          progress: 100,
          status: "done",
        });
        resolve(response.data.data);
      }
      else {
        const errorMsg = `上传失败: HTTP ${response?.status}, 响应: ${JSON.stringify(response?.data)}`;
        console.error(errorMsg);
        onFailed?.({
          ...fileInfo,
          status: "error",
          error: errorMsg,
        });
        reject(new Error(errorMsg));
      }
    }).catch((err) => {
      if (err instanceof CanceledError) {
        console.log("cancel request...");
        onFailed?.({ ...fileInfo, status: "error", error: "请求被取消" });
        resolve(null);
      }
      else {
        const errorMsg = `网络错误: ${err.message || "未知错误"}`;
        console.error("上传错误详情:", err);
        onFailed?.({
          ...fileInfo,
          status: "error",
          error: errorMsg,
          originalError: err,
        });
        reject(err);
      }
    });
  });
  return promise;
};

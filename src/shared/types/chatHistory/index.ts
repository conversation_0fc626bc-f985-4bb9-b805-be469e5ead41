import { SerializedEditorState } from "lexical";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { IChatModelType } from "../business";

export type IChatType = "ChatGPT" | "intelligentChat";

export type IActionResult = {
  link: string;
  title: string;
};
export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}

/**
 * @deprecated 使用 `ICachedMessageAnswer` | `ICachedMessageQuestion`
 */
type ICachedMessage = {
  /**
   * id: 所属的 QAItem 的 id
   */
  id: string;
  isSelf: boolean;
  /**
   * 会用于 旧版 chat 的显示, 新版&旧版的历史上下文
   */
  question: string;
  modelType: IChatModelType;
  indexing?: boolean;
  reply?: string;
  // 代码区块
  section?: any;
  // 文件路径
  fullPath?: string;
  // 当条对话的模型
  chatType?: IChatType;
  likeStatus?: "like" | "unlike" | "cancel";
  // 回答中的链接
  actionResults?: IActionResult[];
  fileList?: UploadFile[];
  /** 额外的代码 */
  code?: string;
  /** 开始行数 */
  startLine?: number;
  /** 结束行数 */
  endLine?: number;
  /** 语言 */
  language?: string;
  /** 文件名 */
  filename?: string;
  formatQuestion?: string;
  codeSearchList?: CodeSearchRelate[];
};

export type ICachedMessageQuestionV1 = ICachedMessage & {
  /* 为什么要显式标注v2？ 方便代码检索旧版 message，在类型定义的时候也不容易出错 */
  v2: undefined;
};

export interface AutoThinkInfo {
  judge: string;
  thinkOn: "on" | "off" | "unset";
  think: string;
  content: string;
  cost: number;
}

/**
 * V2 版本 question 数据结构，核心变更：
 * 使用 editorState 渲染，和用户输入问题使用的编辑器使用同样的结构
 */
export type ICachedMessageQuestionV2 = Omit<ICachedMessage, "fileList" | "code" | "startLine" | "endLine" | "language" | "filename"> & {
  v2: true;
  plainText: string;
  editorState: SerializedEditorState;
  contextItems: MentionNodeV2Structure[];
};

export type ICachedMessageQuestion = ICachedMessageQuestionV1 | ICachedMessageQuestionV2;

export type FileStateType =
/*
初始态, 或其他一切异常状态, 都可以置于此.
注: applying 等中间状态不必存储, 组件内维护就好, 毕竟如果有中断也无法继续 */
  | "init"
/* 已被用户接受 */
  | "accepted" | "rejected";

export interface ICachedMessageAnswer extends ICachedMessage {
  answerId: string;
  /**
   * composer 模式使用, 标记文件的apply状态, 但或许也可以被普通 chat 对话使用
   *
   * 注: `isLatestRevision`(是否是最新版本的 diff): 一般情况下, 最新的对话中的 diff 就是最新的版本. 如果用户手动选择了先前的版本P, 则最新版本变为P
   *
   * 这个状态不需要持久化, 组件维护即可
   */
  affectedFileState?: Record</* 文件路径 */string, {
    state: FileStateType;
  }>;

  /**
   * 代码折叠状态
   * key: 文件路径
   * value: 折叠状态
   */
  codeExpanded?: {
    [key: string]: boolean;
  };
  ttfb: number; // time to first byte, 用于计算思考时间
  thinkInfo?: AutoThinkInfo;
  /**
   * finish_reason：
   * * length token 超长 中断
   * * aborted 用户主动中断
   */
  finishReason: "length" | "aborted" | null;
}

export interface CodeSearchRelate {
  code: string;
  endColNo: number;
  endLineNo: number;
  id: number;
  language: string;
  path: string;
  startColNo: number;
  startLineNo: number;
}
export type QAItem = { Q: ICachedMessageQuestion; A: ICachedMessageAnswer[]; id: string };

export type SessionItem = {
  cachedMessages: QAItem[];
  sessionName: string;
  sessionTime: string;
  sessionId: string;
  expiredIndex: number[];
  clearContextIndex: number[];
  isComposer: boolean;
};

export type BriefSessionItem = Pick<
  SessionItem,
  "sessionId" | "sessionName" | "sessionTime" | "isComposer"
> & {
  isComposerV2: boolean;
  workspaceUri: string;
};

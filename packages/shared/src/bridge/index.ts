import { RequestMessage, ResponseMessage } from "./protocol-observable";
import { CodeActionParams, CodeSection, EditorConfig, InlineChatInfo, UserInfo } from "../misc";
import { ReadDirectoryResult } from "../misc/file";
import { LoggerSupplementaryField, ReportKeys, ReportOpt } from "../misc/logger";
import { UploadFile } from "../misc/textarea";
import { WorkspaceState } from "../state-manager/types";
import { ComposerState, DiffContent, InternalLocalMessage } from "../agent";
import { PersistedComposerHistory } from "../agent/storage";
import { MessageJSONStructure } from "./rpcProtocol";
import { Theme } from "../theme";
import { InputBlockCodePath, OutPutBlockCodePath, Position } from "../misc/blockcode";

export interface BridgeMessage {
  id: string;
  event: string;
  payload?: any;
  code?: number;
  msg?: string;
}

export type BridgeCallback = (payload?: any) => void;

/**
 * native->webview
 */
export enum WEBVIEW_BRIDGE_EVENT_NAME {
  EXTENSIONS_BRIDGE_READY = "extensionHostReady",
  SWITCH_THEME = "switchTheme",
  ACTION_FOR_CODE = "actionForCode",
  CHANGE_ACTIVE_TEXT_EDITOR = "changeActiveTextEditor",
  ACTIVE_PROJECTS_NAME = "activeProjectsName",
  GET_AND_WATCH_USER_INFO_CALLBACK = "getAndWatchUserInfoCallback",
  GET_AND_WATCH_EDITOR_CONFIG_CALLBACK = "getAndWatchEditorConfigCallback",
  PREDICTION_IMAGE = "predictionImage",
  INLINE_CHAT = "inlineChat",
  REPORT_USER_ACTION = "reportUserAction",
  /**
   * 响应 observable 请求 具体查看 [observable-spec.md](./observable-spec.md)
   */
  OBSERVABLE_RESPONSE = "observable/res",

  /** 助理模式 状态更新, 例如模型回答有更新、工具执行结果有更新 */
  COMPOSER_STATE_UPDATE = "composerStateUpdate",

  /** 助理模式 流式更新某条消息 */
  COMPOSER_PARTIAL_MESSAGE = "composerPartialMessage",

  RPC_MESSAGE = "rpcMessage",
}

/**
 * webview->native
 */
export enum NATIVE_BRIDGE_EVENT_NAME {
  GET_CURRENT_FILE_PATH_AND_REPO_PATH = "getCurrentFilePathAndRepoPath",
  GET_OPEN_TAB_FILES = "getOpenTabFiles",
  WEBVIEW_BRIDGE_READY = "webviewBridgeReady",
  OPEN_URL = "openUrl",
  SHOW_TOAST = "showToast",
  COPY_TO_CLIPBOARD = "copyToClipboard",
  GET_SYSTEM_INFO = "getSystemInfo",
  PRINT_LOG = "printLog",
  GET_GIT_INFO = "getGitInfo",
  GET_AND_WATCH_USER_INFO = "getAndWatchUserInfo",
  GET_ACTIVE_EDITOR = "getActiveEditor",
  GET_SELECTION_CONTENT = "getSelectionContent",
  EXECUTE_CMD = "executeCmd",
  GET_WORKSPACE_URI = "getWorkspaceUri",
  GET_STATE = "getState",
  UPDATE_STATE = "updateState",
  READ_FILE = "readFile",
  GET_FILE_STATUS = "getFileStatus",
  UPLOAD_FILE = "uploadFile",
  PRINT_LOGGER = "printLogger",
  SET_ACTIVE_PROJECT_NAME = "setActiveProjectName",
  /** 历史记录相关 */
  ADD_SESSION = "addSession",
  DELETE_SESSION = "deleteSession",
  CLEAR_SESSION = "clearSession",
  UPDATE_SESSION = "updateSession",
  GET_SESSION = "getSession",

  ADD_MESSAGE = "addMessage",
  UPDATE_MESSAGE = "updateMessage",
  LOGIN = "login",

  /** 内联聊天 */
  STREAM_DIFF_MESSAGE = "streamDiffMessage",
  /** 获取配置 */
  GET_CONFIG = "getConfig",

  /** 编辑区操作 */
  CODE_INSERT = "codeInsert",
  CODE_DIFF = "codeDiff",
  ACCEPT_REJECT_DIFF = "acceptRejectDiff",
  /** 获取文件路径或代码块的路径信息 */
  GET_FILEPATH_OF_BLOCKCODE = "getFilepathOfBlockcode",
  /** 打开文件到编辑区 */
  OPEN_FILE_TO_EDITOR = "openFileToEditor",
  /** 打开绝对路径的文件到编辑区 */
  OPEN_FILE_TO_EDITOR_ABSOLUTE_PATH = "openFileToEditorAbsolutePath",
  /** 打开文件到编辑区，可能使用 diff 编辑器 */
  OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR = "openFileToEditorMaybeDiffEditor",
  /** 打开跳转到 symbol */
  OPEN_SYMBOL_IN_FILE = "openSymbolInFile",
  /** 读取目录下所有文件 */
  READ_DIRECTORY = "readDirectory",
  CLEAR_INLINE_INFO = "clearInlineInfo",
  /* 预览 artifact */
  ARTIFACT_PREVIEW = "artifactPreview",

  GET_AND_WATCH_EDITOR_CONFIG = "getAndWatchEditorConfig",

  /** agent 相关 */
  INDEX_FILE = "indexFile",
  REPO_INDEXING = "repoIndexing",
  INDEX_BUILD = "indexBuild",
  INDEX_PAUSE = "indexPause",
  INDEX_CLEAR_INDEX = "indexClearIndex",
  INDEX_REPO_INDEX = "indexRepoIndex",
  STATE_CHECK_REPO_STATE = "stateCheckRepoState",
  SEARCH_SEARCH = "searchSearch",
  SEARCH_SEARCH_BY_PATH = "searchSearchByPath",
  /** 打开设置 */
  OPEN_SETTING = "openSetting",
  /**
   * 请求获取 observable 类型的数据， 具体查看 [observable-spec.md](./observable-spec.md)
  **/
  OBSERVABLE_REQUEST = "observable/req",
  /** 在终端中执行命令 */
  TERMINAL_SEND_TEXT = "terminal.sendText",

  /** 获取助理模式历史记录 */
  COMPOSER_HISTORY_GET = "composerHistoryGet",

  /** 清除任务 */
  COMPOSER_CLEAR_TASK = "composerClearTask",

  /** 获取当前工作区storageUri路径 */
  GET_WORKSPACE_STORAGE_URI = "getWorkspaceStorageUri",

  COMPOSER_UPDATE_SESSION_NAME = "composerUpdateSessionName",
  /** 用于差异视图的反馈 */
  COMPOSER_DIFF_FEEDBACK = "composerDiffFeedback",
  /** 存储差异视图信息 */
  COMPOSER_DIFF_SAVE = "composerDiffSave",
  /** 用于应用/重新应用文件 */
  COMPOSER_APPLY_FILE = "composerApplyFile",
  /** 用于切换终端 */
  COMPOSER_TOGGLE_TERMINAL = "composerToggleTerminal",
  /** 普通模式apply，兼容xcode */
  FILE_EDIT = "fileEdit",

  RPC_MESSAGE = "rpcMessage",
  /** 获取主题某个主题的配置 */
  GET_THEME_SETTINGS = "getThemeSettings",

  /** 打开面板 */
  REVEAL_PANEL = "revealPanel",
}

type Merge<T, U> = {
  [P in keyof T | keyof U]: P extends keyof U
    ? U[P]
    : P extends keyof T
      ? T[P]
      : never;
};

/**
 * 为 bridge 通信添加强类型约束，包含 webview 消息的 payload 和 可能的 callback
 * 后续所有的 bridge 通信都应当把类型定义在这里
 */
export interface NativeBridgeEventMap {
  [NATIVE_BRIDGE_EVENT_NAME.GET_STATE]: (payload: { key: WorkspaceState }) => any;
  [NATIVE_BRIDGE_EVENT_NAME.OBSERVABLE_REQUEST]: (payload: RequestMessage) => void;
  [NATIVE_BRIDGE_EVENT_NAME.WEBVIEW_BRIDGE_READY]: (payload: void) => void;
  [NATIVE_BRIDGE_EVENT_NAME.GET_AND_WATCH_USER_INFO]: () => UserInfo;
  [NATIVE_BRIDGE_EVENT_NAME.GET_AND_WATCH_EDITOR_CONFIG]: () => EditorConfig;
  [NATIVE_BRIDGE_EVENT_NAME.GET_SYSTEM_INFO]: () => {
    hostname: string;
    ideVersion: string;
    pluginVersion: string;
    platform: string;
    release: string;
    deviceId: string;
    ide: string;
    machine: string;
    arch: string;
    version: string;
  };
  [NATIVE_BRIDGE_EVENT_NAME.GET_ACTIVE_EDITOR]: () => {
    document: {
      fileName: string;
      relativePath: string;
      language: string;
    };
  };
  [NATIVE_BRIDGE_EVENT_NAME.INDEX_FILE]: (payload: {
    file: string;
    action: "modify" | "delete" | "create";
  }) => void;

  [NATIVE_BRIDGE_EVENT_NAME.REPO_INDEXING]: () => boolean;
  [NATIVE_BRIDGE_EVENT_NAME.STATE_CHECK_REPO_STATE]: (payload: {
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.SEARCH_SEARCH]: (payload: {
    query: string;
    chatHistory: {
      role: "user" | "assistant";
      content: string;
    }[];
    topK?: number;
    targetDirectory?: string[];
  }) => any;

  [NATIVE_BRIDGE_EVENT_NAME.GET_SELECTION_CONTENT]: () => {
    language: string;
    code: string;
    filename: string;
    startLine: number;
    endLine: number;
  };
  [NATIVE_BRIDGE_EVENT_NAME.EXECUTE_CMD]: (payload: { cmd: string }) => { result: string };
  [NATIVE_BRIDGE_EVENT_NAME.GET_WORKSPACE_URI]: () => { result: string };
  [NATIVE_BRIDGE_EVENT_NAME.GET_WORKSPACE_STORAGE_URI]: () => { result: string };
  [NATIVE_BRIDGE_EVENT_NAME.UPDATE_STATE]: (payload: { key: WorkspaceState; value: any }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.READ_FILE]: (payload: { filePath: string }) => { content: string } | void;
  [NATIVE_BRIDGE_EVENT_NAME.GET_FILE_STATUS]: (payload: { filePath: string }) => { status: any } | void;
  [NATIVE_BRIDGE_EVENT_NAME.UPLOAD_FILE]: () => { fileInfo: UploadFile[] } | undefined | void;
  [NATIVE_BRIDGE_EVENT_NAME.PRINT_LOGGER]: (payload: {
    level: "silly" | "debug" | "verbose" | "info" | "warn" | "error";
    msg: string;
    scope: string;
    tags?: LoggerSupplementaryField;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.GET_SESSION]: (payload: any) => any;
  [NATIVE_BRIDGE_EVENT_NAME.GET_OPEN_TAB_FILES]: () => {
    list: string[];
  };
  [NATIVE_BRIDGE_EVENT_NAME.GET_CONFIG]: (payload: { key: string }) => any;
  [NATIVE_BRIDGE_EVENT_NAME.STREAM_DIFF_MESSAGE]: (payload: {
    filename?: string;
    message: string;
    autoCreate?: boolean;
    autoOpen?: boolean;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.READ_DIRECTORY]: (payload: {
    path: string;
    options: { recursive: boolean; excludes: string[] };
  }) => ReadDirectoryResult | void;
  [NATIVE_BRIDGE_EVENT_NAME.ARTIFACT_PREVIEW]: (payload: ArtifactPreviewBridgeData) => void;
  [NATIVE_BRIDGE_EVENT_NAME.OPEN_URL]: (payload: { url: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.SHOW_TOAST]: (payload: { message: string; level: "error" | "info" }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.COPY_TO_CLIPBOARD]: (payload: { text: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.CODE_INSERT]: (payload: { content: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.CODE_DIFF]: (payload: {
    content: string;
    section: CodeSection;
    fullPath: string;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.ACCEPT_REJECT_DIFF]: (payload: {
    accept: boolean;
    filepath?: string;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK]: (payload: {
    type: "keep" | "undo";
    filepath?: string;
    partialPaths?: string[]; // 是否是部分接受或拒绝，主要用于 IDE
    abortChat?: boolean;
    // 部分拒绝时，需要传入文件状态
    filesStatus?: { [filepath: string]: "accepted" | "rejected" };
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_SAVE]: (payload: {
    ts: number;
    filepath: string;
    diffContent: DiffContent | undefined;
  }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_APPLY_FILE]: (payload: { message: InternalLocalMessage }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR]: (payload: { filepath: string; startLine?: number; endLine?: number }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.GET_FILEPATH_OF_BLOCKCODE]: (payload: { content: string; sessionId: string; pos?: Position }) => Promise<OutPutBlockCodePath | undefined>;
  [NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_ABSOLUTE_PATH]: (payload: { filepath: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR]: (payload: { filepath: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.OPEN_SYMBOL_IN_FILE]: (payload: { filepath: string; symbolText: string; range?: InputBlockCodePath["range"] }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.TERMINAL_SEND_TEXT]: (payload: {
    text: string;
  }) => {
    success: boolean;
    message?: string;
  };
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_HISTORY_GET]: () => PersistedComposerHistory;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_CLEAR_TASK]: () => void;
  [NATIVE_BRIDGE_EVENT_NAME.DELETE_SESSION]: (payload: { sessionId: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.CLEAR_SESSION]: () => void;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_UPDATE_SESSION_NAME]: (payload: { sessionId: string; name: string }) => void;
  [NATIVE_BRIDGE_EVENT_NAME.FILE_EDIT]: (payload: {
    filename: string;
    modelOutput: string;
    sessionId: string;
    chatId: string;
    applyId: string;
  }) => undefined;
  [NATIVE_BRIDGE_EVENT_NAME.COMPOSER_TOGGLE_TERMINAL]: () => void;
  [NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE]: (payload: MessageJSONStructure) => void;

  [NATIVE_BRIDGE_EVENT_NAME.GET_THEME_SETTINGS]: (payload: { themeId: string; themeKind: string }) => Theme | undefined;
}

export type NativeBridgeParams = Merge<
  Record<NATIVE_BRIDGE_EVENT_NAME, void>,
  {
    [NATIVE_BRIDGE_EVENT_NAME.GET_STATE]: { key: string };
  }
>;

export type ExtractNativeBridgePayload<T extends NATIVE_BRIDGE_EVENT_NAME> = T extends keyof NativeBridgeEventMap ? Parameters<NativeBridgeEventMap[T]>[0] : unknown;

export type ExtractNativeBridgeResult<T extends NATIVE_BRIDGE_EVENT_NAME> = T extends keyof NativeBridgeEventMap ? ReturnType<NativeBridgeEventMap[T]> : unknown;

export type WebviewBridgeResult = Merge<
  Record<WEBVIEW_BRIDGE_EVENT_NAME, undefined>,
  {
    [WEBVIEW_BRIDGE_EVENT_NAME.SWITCH_THEME]: { theme: "dark" | "light" };
    [WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE]: CodeActionParams;
    [WEBVIEW_BRIDGE_EVENT_NAME.CHANGE_ACTIVE_TEXT_EDITOR]: {
      document: {
        fileName: string;
        relativePath: string;
        languageId: string;
      };
    };
    [WEBVIEW_BRIDGE_EVENT_NAME.ACTIVE_PROJECTS_NAME]: { list: string[] };
    [WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_USER_INFO_CALLBACK]: UserInfo;
    [WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_EDITOR_CONFIG_CALLBACK]: EditorConfig;
    [WEBVIEW_BRIDGE_EVENT_NAME.PREDICTION_IMAGE]: {
      sourceBlockContent: string;
      targetBlockContent: string;
      languageId: string;
      editorConfig: EditorConfig;
    };
    [WEBVIEW_BRIDGE_EVENT_NAME.INLINE_CHAT]: InlineChatInfo | undefined;
    [WEBVIEW_BRIDGE_EVENT_NAME.REPORT_USER_ACTION]: ReportOpt<keyof ReportKeys>;
    [WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE]: void;
    [WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE]: void;
    [WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE]: void;
  }
>;
export type WebviewBridgeParams = Merge<
  Record<WEBVIEW_BRIDGE_EVENT_NAME, undefined>,
  {
    [WEBVIEW_BRIDGE_EVENT_NAME.INLINE_CHAT]: InlineChatInfo | undefined;
    [WEBVIEW_BRIDGE_EVENT_NAME.PREDICTION_IMAGE]: {
      sourceBlockContent: string;
      targetBlockContent: string;
      languageId: string;
      editorConfig: EditorConfig;
    };
    [WEBVIEW_BRIDGE_EVENT_NAME.CHANGE_ACTIVE_TEXT_EDITOR]: {
      document: {
        fileName: string;
        relativePath: string;
        languageId: string;
      };
    };
    [WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_USER_INFO_CALLBACK]: UserInfo | undefined;
    [WEBVIEW_BRIDGE_EVENT_NAME.GET_AND_WATCH_EDITOR_CONFIG_CALLBACK]: EditorConfig;
    [WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE]: CodeActionParams;
    [WEBVIEW_BRIDGE_EVENT_NAME.SWITCH_THEME]: { theme: "dark" | "light" };
    [WEBVIEW_BRIDGE_EVENT_NAME.REPORT_USER_ACTION]: ReportOpt<keyof ReportKeys>;
    [WEBVIEW_BRIDGE_EVENT_NAME.OBSERVABLE_RESPONSE]: ResponseMessage;
    [WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE]: ComposerState;
    [WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE]: {
      partialMessage: InternalLocalMessage;
    };
    [WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE]: MessageJSONStructure;

  }
>;

export type ArtifactPreviewBridgeData = {
  eventType: "start" | "data";
  data: ArtifactPreviewData;
} | {
  eventType: "end";
  // 在触发 end 事件事, 需要了解当前的 provider 是谁, 否则不知道向谁发送 end 事件
  provider: ArtifactProvider;
} | {
  eventType: "error";
  // 在触发 error 事件事, 需要了解当前的 provider 是谁, 否则不知道向谁发送 error 事件
  provider: ArtifactProvider;
  error: unknown;
};

export type ArtifactProvider =/* 商业化 navi @shilin05 */ "navi";
export interface ArtifactPreviewData {
  provider: ArtifactProvider;
  artifact: {
    id: string; // artifact id
    title: string; // artifact 名称
    attr: Record<string, string>; // artifact 标签属性
    actions: {
      // artifact action 信息
      content: string; // 代码
      // 以下字段会自动带入标签中的数据，例如 <kwaipilotAction filePath="index.js"> 就会有 filePath
      attr: Record<string, string>; // action 标签属性
      // 是否生成完毕
      closed: boolean;
    }[];

  };
}

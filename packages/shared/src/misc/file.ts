export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}

export interface BridgeUploadFile {
  uri: string;
  relativePath: string;
  uploadInfo: UploadFile;
}

export interface ReadDirectoryResult {
  files: string[];
}

export interface FileData {
  name: string;
  type: string;
  data: ArrayBuffer | string; // 支持ArrayBuffer（本地模式）和base64字符串（独立窗口模式）
  /** @IMP: 由于特殊通信的限制，在kwaipilot ide环境下 非 string的数据并不能传递过来，因此这里暂时打个补丁 */
  uri?: string;
}

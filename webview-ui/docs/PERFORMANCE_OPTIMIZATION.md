# HistoryBar 组件性能优化
## 问题描述

原先的 `HistoryBar` 组件在每次导航切换时都会重新加载，特别是 `isPanel` 状态会重新从桥接API获取，导致用户体验不佳。

## 问题原因

1. **组件重新挂载**：每次路由切换时，页面组件完全重新挂载
2. **Observable重新订阅**：`useBridgeObservableAPI("viewModel")` 在每次挂载时都会重新订阅
3. **状态重新获取**：`isPanel` 状态每次都需要重新从桥接API获取

## 优化方案

### 1. 创建全局状态管理

创建了 `useViewModelStore` 来管理全局的 `viewModel` 状态：

```typescript
// webview-ui/src/store/viewModel.ts
export const useViewModelStore = create<ViewModelState>((set, get) => ({
  viewModel: null,
  isInitialized: false,
  setViewModel: (viewModel) => set({ viewModel }),
  initialize: () => {
    // 创建全局订阅，避免重复订阅
    globalSubscription = kwaiPilotBridgeAPI.observableAPI
      .viewModel()
      .subscribe((newViewModel) => {
        set({ viewModel: newViewModel, isInitialized: true });
      });
  },
}));
```

### 2. 应用级别初始化

在 `App.tsx` 中初始化 viewModel store，确保在应用启动时就建立全局订阅：

```typescript
// 在 App 组件的 useEffect 中
useEffect(() => {
  // 初始化 viewModel store
  initializeViewModel();
  // ... 其他初始化逻辑
}, [initializeViewModel]);
```

### 3. 创建便捷 Hook

创建了 `useViewModel` hook 来简化使用：

```typescript
// webview-ui/src/hooks/useViewModel.ts
export function useViewModel() {
  const { viewModel, initialize, isInitialized } = useViewModelStore();
  
  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [initialize, isInitialized]);
  
  return {
    viewModel,
    isPanel: viewModel === "panel",
    isTab: viewModel === "tab",
    isInitialized,
  };
}
```

### 4. 组件优化

更新 `HistoryBar` 组件使用新的状态管理：

```typescript
function ViewModelChange({ shouldBlock, onBlockedContinue, hasIndeterminatedWorkingSet }: ViewModelChangeProps) {
  // 使用优化后的 useViewModel hook
  const { isPanel } = useViewModel();
  
  // ... 其他逻辑保持不变
}
```

## 优化效果

### 之前的问题
- ❌ 每次导航都重新订阅 viewModel observable
- ❌ 每次导航都重新获取 isPanel 状态
- ❌ 导致不必要的网络请求和状态重置
- ❌ 用户体验差，切换时有明显的加载延迟

### 优化后的改进
- ✅ 全局单例订阅，避免重复订阅
- ✅ 状态在导航间保持，无需重新获取
- ✅ 减少了不必要的桥接API调用
- ✅ 用户体验流畅，导航切换无延迟

## 使用方式

### 在组件中使用 viewModel 状态

```typescript
import { useViewModel } from '@/hooks/useViewModel';

function MyComponent() {
  const { isPanel, isTab, viewModel } = useViewModel();
  
  return (
    <div>
      {isPanel ? '面板模式' : '标签模式'}
    </div>
  );
}
```

### 性能监控

如果需要监控性能，可以使用提供的性能监控工具：

```typescript
import { performanceMonitor } from '@/utils/performance';

// 监控函数执行时间
performanceMonitor.measure('component_render', () => {
  // 组件渲染逻辑
});

// 在 React 组件中使用
import { usePerformanceMonitor } from '@/utils/performance';

function MyComponent() {
  const { start, end } = usePerformanceMonitor('MyComponent');
  
  useEffect(() => {
    start();
    // 渲染逻辑
    end();
  }, []);
}
```

## 注意事项

1. **内存管理**：全局订阅会在整个应用生命周期中保持，确保在应用卸载时正确清理
2. **错误处理**：如果桥接API不可用，store 会优雅降级
3. **开发模式**：性能监控工具只在开发模式下启用

## 相关文件

- `webview-ui/src/store/viewModel.ts` - viewModel 状态管理
- `webview-ui/src/hooks/useViewModel.ts` - 便捷使用 hook
- `webview-ui/src/components/HistoryBar/index.tsx` - 优化后的组件
- `webview-ui/src/utils/performance.ts` - 性能监控工具
- `webview-ui/src/App.tsx` - 应用级别初始化

import { useMemo } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
// import { ExtensionAction } from "@shared/types/channel/extension";
import { useUserStore } from "@/store/user";
import { Tooltip } from "@/components/Union/chakra-ui";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { chakra } from "@chakra-ui/react";
import { useContextHeaderContext } from "@/logics/UserInputTextarea/ContextHeader/ContextHeaderContext";
import { useComposerModelSupport } from "@/hooks/useComposerModelSupport";

export const UploadImageBtn: React.FC = () => {
  const userInfo = useUserStore(state => state.userInfo);
  const { currentModelDetail } = useComposerModelSupport();

  const { state: contextHeaderState } = useContextHeaderContext();

  const handleUpload = async () => {
    if (!userInfo) {
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: "请先登录",
      });
      return;
    }
    if (disable) return;
    const limit = 10 - (contextHeaderState.nodes.filter(node => node.structure.type === "remoteImage").length || 0);
    const params: ReportOpt<"uploadImage"> = {
      key: "uploadImage",
      type: undefined,
    };
    reportUserAction(params);
    const files = await kwaiPilotBridgeAPI.extensionMisc.$uploadImage(limit);
    files.forEach((file) => {
      console.log("上传的图片信息", file);
      contextHeaderState.tryInsertNode({
        structure: {
          type: "remoteImage",
          uri: file.uri,
          relativePath: file.relativePath,
          uploadInfo: file.uploadInfo,
        },
        isVirtualContext: false,
        followActiveEditor: false,
      }, {
        source: "context",
      });
    });
  };

  const disable = useMemo(() => {
    // 用户未登录
    if (!userInfo) {
      return true;
    }

    // 当前模型不支持图片上传
    if (currentModelDetail && currentModelDetail.supportImage === false) {
      return true;
    }

    return false;
  }, [userInfo, currentModelDetail]);

  // 根据禁用状态生成不同的提示文本
  const tooltipLabel = useMemo(() => {
    if (!userInfo) {
      return "请先登录";
    }

    if (currentModelDetail && currentModelDetail.supportImage === false) {
      return `当前模型 ${currentModelDetail.name} 不支持图片上传`;
    }

    return "添加图片";
  }, [userInfo, currentModelDetail]);

  return (
    <>
      <Tooltip label={tooltipLabel}>
        <chakra.button
          className="cursor-pointer flex items-center w-6 h-6 bg-transparent rounded hover:bg-button-secondaryHoverBackground disabled:bg-transparent disabled:cursor-not-allowed disabled:opacity-50"
          onClick={handleUpload}
          disabled={disable}
        >
          <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_739_21794)">
              <path fillRule="evenodd" clipRule="evenodd" d="M13.5 5.84902L16.9 9.34902L17 9.74902V11.749H16V10.749H12V6.74902H7V17.749H11V18.749H6.5L6 18.249V6.24902L6.5 5.74902H13.2L13.5 5.84902ZM13 6.74902V9.74902H15.9L13 6.74902ZM17 20.749H16V17.749H13V16.749H16V13.749H17V16.749H20V17.749H17V20.749Z" fill="currentColor" />
            </g>
            <defs>
              <clipPath id="clip0_739_21794">
                <rect width="16" height="16" fill="white" transform="translate(4 4.74902)" />
              </clipPath>
            </defs>
          </svg>
        </chakra.button>
      </Tooltip>
    </>
  );
};

import { useEffect, memo, useState } from "react";
import { DecorationItem } from "shiki";
import {
  addLineHeightToPreStyle,
  getHighlighterInstance,
} from "@/utils/highlighter";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { logger } from "@/utils/logger";
import { getShikiLanguage } from "@/utils/language2shikiLang";
import "./index.css";
import clsx from "clsx";
import { DiffContent } from "shared/lib/agent";
import ArrowDownIcon from "@/assets/codeblock/arrowDown.svg?react";
import { useTrustedHTML } from "@/hooks/useTrustedHTML";
import { useShikiTheme } from "@/hooks/useShikiTheme";

export interface IDiffCodeProps {
  language?: string;
  diffContent?: DiffContent;
  isCollapsed?: boolean;
  handleToggleCollapse?: () => void;
}

export function DiffCode(props: IDiffCodeProps) {
  const {
    language = "typescript",
    diffContent,
    isCollapsed,
    handleToggleCollapse,
  } = props;
  const editorConfig = useVsEditorConfig(state => state.editorConfig);
  const theme = useShikiTheme();
  const [code, setCode] = useState<string>("");
  const [shouldShowCollapseButton, setShouldShowCollapseButton]
    = useState(isCollapsed);
  const contentRef = useTrustedHTML(code);
  const maxHeight = 212; // 与 maxHeight 样式保持一致

  useEffect(() => {
    window.setTimeout(() => {
      // 检测内容高度是否超过最大高度
      if (contentRef.current) {
        const contentHeight = contentRef.current.scrollHeight;
        setShouldShowCollapseButton(contentHeight > maxHeight);
      }
    }, 100);
  }, [code, contentRef]);

  // 代码高亮处理
  useEffect(() => {
    const renderHighlight = async () => {
      if (!diffContent || !diffContent.code) return;
      try {
        // 创建装饰器 - 只使用每行的实际长度
        const decorations: DecorationItem[] = [];

        // 使用shiki高亮代码
        const html = getHighlighterInstance().codeToHtml(diffContent.code, {
          theme,
          lang: getShikiLanguage(language),
          decorations,
          transformers: [
            {
              code(node) {
                node.properties.style = `display: grid;background-color: transparent;`;
              },
              line(node, line) {
                node.properties.style = `padding: 0 12px;height: 18px;line-height: 18px;`;
                if (diffContent.deletedLines.includes(line)) {
                  node.properties.style
                    = "background-color: var(--vscode-diffEditor-removedLineBackground);padding: 0 12px;height: 18px;line-height: 18px;";
                }
                else if (diffContent.addedLines.includes(line)) {
                  node.properties.style
                    = "background-color: var(--vscode-diffEditor-insertedLineBackground);padding: 0 12px;height: 18px;line-height: 18px;";
                }
              },
            },
          ],
        });
        setCode(addLineHeightToPreStyle(html, editorConfig.fontSize * 1.5));
      }
      catch (error) {
        setCode(diffContent.code);
        logger.error("Failed to highlight diff code:", "diffCodeComponent", {
          err: error,
        });
      }
    };
    renderHighlight();
  }, [diffContent, language, editorConfig.theme, editorConfig.fontSize, theme]);

  return (
    <div>
      <div
        ref={contentRef}
        className={clsx(
          "diff-code",
          "min-w-[236px] w-full overflow-x-auto",
          isCollapsed
            ? `overflow-x-hidden overflow-y-hidden max-h-[212px]`
            : "",
          `custom-scrollbar-${editorConfig.theme}`,
          `bg-[var(--vscode-editor-background)]`,
        )}
      />
      {shouldShowCollapseButton && (
        <div
          onClick={handleToggleCollapse}
          className="bg-[var(--vscode-editor-background)] w-full h-[20px] cursor-pointer flex items-center justify-center text-[var(--vscode-foreground)]"
          style={{
            backdropFilter: isCollapsed ? "blur(0.5px)" : "none",
          }}
        >
          {isCollapsed
            ? (
                <ArrowDownIcon className="w-[14px] h-[14px] " />
              )
            : (
                <ArrowDownIcon className="w-[14px] h-[14px] transform rotate-180" />
              )}
        </div>
      )}
    </div>
  );
}
export default memo(DiffCode);

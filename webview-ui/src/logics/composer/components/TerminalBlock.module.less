@CODE_BLOCK_BG_COLOR: var(--vscode-editor-background, --vscode-sideBar-background, rgb(30 30 30));
.nonePreBg{
  pre {
    background: none !important;
    padding: 0 !important;
  }
}

.nonePreTopBorder{
  pre {
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }
}

.markdownBody {
  pre {
    border-radius: 5px;
    margin: 0;
    min-width: max-content;
    padding: 0 12px;
    background-color: transparent;
  }

  &.forceWrap {

    pre, code {
      white-space: pre-wrap;
      word-break: break-all;
      overflow-wrap: anywhere;
    }
    pre {
      min-width: auto;
    }
  }

  pre>code {
    .hljs-deletion {
      background-color: var(--vscode-diffEditor-removedTextBackground);
      display: inline-block;
      width: 100%;
    }

    .hljs-addition {
      background-color: var(--vscode-diffEditor-insertedTextBackground);
      display: inline-block;
      width: 100%;
    }
  }

  code {
    background-color: transparent;
    span.line:empty {
      display: none;
    }

    word-wrap: break-word;
    border-radius: 5px;
    font-size: 13px;
    font-family: var(--vscode-editor-font-family);
  }

  code:not(pre > code) {
    font-family: var(--vscode-editor-font-family);
    color: #f78383;
  }

  // 隐藏滚动条的样式
  div, textarea {
    &::-webkit-scrollbar {
      display: none;
    }
    
    scrollbar-width: none;
    
    -ms-overflow-style: none;
  }

  font-family: var(--vscode-font-family),
  system-ui,
  -apple-system,
  BlinkMacSystemFont,
  "Segoe UI",
  Roboto,
  Oxygen,
  Ubuntu,
  Cantarell,
  "Open Sans",
  "Helvetica Neue",
  sans-serif;
  font-size: 13px;
  color: var(--vscode-editor-foreground, #fff);

  p,
  li,
  ol,
  ul {
    line-height: 1.5;
  }
}

import { <PERSON>over, Toolt<PERSON> } from "@/components/Union/chakra-ui";
import { PopoverContent, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import { useCallback } from "react";
import { SupportedModels } from "shared/lib/agent/supportedModels";
import { kwaiPilotBridgeAPI } from "@/bridge";
import clsx from "clsx";
import { Icon } from "@/components/Union/t-iconify";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { ClientSideModel } from "@/store/record";
import AutoTooltip from "@/components/AutoTooltip";
import { useComposerModelSupport } from "@/hooks/useComposerModelSupport";

export function ModelSelector({ className }: { className?: string }) {
  const { onOpen, onClose, isOpen } = useDisclosure();
  const { modelList, currentModelDetail } = useComposerModelSupport();

  const handleSelect = useCallback(async (model: ClientSideModel) => {
    if (model.disabled) return;
    await kwaiPilotBridgeAPI.extensionComposer.$setCurrentModel(model.modelType as SupportedModels);
    onClose();
  }, [onClose]);

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement="top-start"
      closeOnBlur={true}
      trigger="click"
      boundary="scrollParent"
      computePositionOnMount={true}
      eventListeners={true}
      flip={true}
    >
      <PopoverTrigger>
        <div
          onMouseDown={e => e.preventDefault()}
          className={clsx(
            "flex-auto overflow-hidden flex gap-1 items-center cursor-pointer rounded group",
            className,
          )}
        >
          <div
            className={clsx(
              "flex items-center gap-1 min-w-0 transition-all text-foreground",
            )}
          >
            <AutoTooltip
              title={currentModelDetail?.name}
              className="text-xs flex-shrink min-w-0"
              lineClamp={1}
            >
              {currentModelDetail?.name}
            </AutoTooltip>
            <Icon icon="codicon:chevron-down" className={clsx("flex-shrink-0", isOpen ? "rotate-180" : "")} width="14" height="14" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <CustomScrollBar
          suppressScrollX
          className="h-[224px] w-[280px] border border-settings-dropdownBorder rounded"
        >
          <div className="p-1 w-full flex flex-col gap-1 rounded bg-editor-background text-dropdown-foreground">
            {modelList.map((model, idx) => {
              return (
                <div
                  className={clsx(
                    "w-full rounded-sm px-3 py-2 relative hover:bg-list-dropBackground",
                    model.disabled ? "cursor-not-allowed" : "cursor-pointer",
                  )}
                  onClick={() => handleSelect(model)}
                  key={idx}
                >
                  <Tooltip label={model.tooltip}>
                    <div className="flex gap-1 items-center">
                      <img src={model.icon} className="w-[14px] h-[14px]" />
                      <div
                        className={clsx(" leading-[19.5px] text-[13px]", [
                          model.disabled
                            ? "text-text-common-disable"
                            : "",
                        ])}
                      >
                        {model.name}
                      </div>
                      {model.vipIcon && <img src={model.vipIcon} alt="" />}
                    </div>
                  </Tooltip>
                  <div
                    className={clsx(
                      "text-[12px] leading-[18px]",
                      model.disabled
                        ? " text-menu-foreground"
                        : "",
                    )}
                  >
                    {model.desc}
                  </div>
                  { currentModelDetail?.modelType === model.modelType && (<Icon icon="codicon:check" className=" text-icon-foreground absolute right-[12px] top-1/2 -translate-y-1/2 " width="14" height="14" />)}
                </div>
              );
            })}
          </div>
        </CustomScrollBar>
      </PopoverContent>
    </Popover>
  );
}

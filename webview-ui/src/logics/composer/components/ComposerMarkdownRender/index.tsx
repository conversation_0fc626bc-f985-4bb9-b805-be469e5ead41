import ReactMarkdown, { Components } from "react-markdown";
import { type Root } from "react-markdown/lib/index";
import style from "./index.module.less";
import clsx from "clsx";
import { InlineCode } from "./InlineCode";
import { CodeBlock } from "./CodeBlock";
import { useMemo } from "react";
import RemarkGfm from "remark-gfm";
import rehypeKatex from "rehype-katex";
import rehypeRaw from "rehype-raw";
import { visit, BuildVisitor } from "unist-util-visit";
import remarkMath from "remark-math";
import "katex/dist/katex.min.css";

const addInlineFlagToCode: any = () => {
  const visitor: BuildVisitor<Root> = (tree) => {
    visit(tree, (node, _index, parent) => {
      // 检查节点是否为 code 元素
      if (node.type === "element" && node.tagName === "code") {
        node.properties = node.properties || {};
        node.properties.isInline = !(parent?.type === "element" && parent.tagName === "pre");
      }
    });
  };
  return visitor;
};

interface IProps {
  handleCopy: (text: string) => void;
  content: string;
}

export const ComposerMarkdownRender = ({ content, handleCopy }: IProps) => {
  const components: Components = useMemo(() => {
    return {
      code(props) {
        const { children, className } = props;
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : "plaintext";
        if (props.node?.properties.isInline) {
          return <InlineCode c={children?.toString() || ""} pos={props.node.position}></InlineCode>;
        }

        return <CodeBlock language={language} content={String(children)} handleCopy={handleCopy}></CodeBlock>;
      },
      img(props) {
        // 处理图片加载错误
        return (
          <img
            {...props}
            onError={(e) => {
              const target = e.currentTarget;
              // 添加错误类名用于应用样式
              target.classList.add("error-image");
              // 可选：设置alt文本描述错误
              if (!target.alt) {
                target.alt = "图片加载失败";
              }
              // 阻止继续尝试加载错误图片
              target.onerror = null;
            }}
          />
        );
      },
    };
  }, [handleCopy]);

  return (
    <div className={clsx(style["kwaipilot-markdown"])}>
      <ReactMarkdown
        remarkPlugins={[RemarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex, rehypeRaw, addInlineFlagToCode]}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

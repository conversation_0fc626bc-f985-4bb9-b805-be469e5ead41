import { kwaiPilotBridgeAPI } from "@/bridge";
import AutoTooltip from "@/components/AutoTooltip";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { DOM } from "@/utils/dom";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useComposerTaskContext } from "../../context/ComposerTaskContext";
import { OutPutBlockCodePath, Position } from "shared/lib/misc/blockcode";
import { Icon } from "@/components/Union/t-iconify";
import { useComposerState } from "../../context/ComposerStateContext";
import "./InlineCode.less";

// 创建一个轻量级的内存缓存，用于前端快速响应
const memoryCache = new Map<string, {
  data: OutPutBlockCodePath | undefined;
  timestamp: number;
}>();

// 缓存过期时间：5分钟（比后端缓存短，确保数据一致性）
const MEMORY_CACHE_TTL = 5 * 60 * 1000;

// 生成缓存键
function generateCacheKey(content: string, sessionId: string, pos?: Position): string {
  const posKey = pos ? `${pos.start.line}-${pos.start.column}-${pos.end.line}-${pos.end.column}` : "";
  return `${content}:${sessionId}:${posKey}`;
}

// 获取缓存数据
function getCachedData(cacheKey: string): OutPutBlockCodePath | undefined | null {
  const cached = memoryCache.get(cacheKey);
  if (!cached) return null; // 没有缓存

  const now = Date.now();
  if (now - cached.timestamp > MEMORY_CACHE_TTL) {
    memoryCache.delete(cacheKey); // 过期删除
    return null;
  }

  return cached.data; // 返回缓存数据
}

// 设置缓存数据
function setCachedData(cacheKey: string, data: OutPutBlockCodePath | undefined): void {
  memoryCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
  });
}

// vsode.SymbolKind
enum SymbolKind {
  //  NotFound default
  NotFound = -1,
  File = 0,
  Module = 1,
  Namespace = 2,
  Package = 3,
  Class = 4,
  Method = 5,
  Property = 6,
  Field = 7,
  Constructor = 8,
  Enum = 9,
  Interface = 10,
  Function = 11,
  Variable = 12,
  Constant = 13,
  String = 14,
  Number = 15,
  Boolean = 16,
  Array = 17,
  Object = 18,
  Key = 19,
  Null = 20,
  EnumMember = 21,
  Struct = 22,
  Event = 23,
  Operator = 24,
  TypeParameter = 25,
}

// 根据 SymbolKind 获取对应的 codicon 图标
function getSymbolIcon(symbolKind?: SymbolKind): string {
  switch (symbolKind) {
    case SymbolKind.File:
      return "codicon:file";
    case SymbolKind.Module:
      return "codicon:file-submodule";
    case SymbolKind.Namespace:
      return "codicon:symbol-namespace";
    case SymbolKind.Package:
      return "codicon:package";
    case SymbolKind.Class:
      return "codicon:symbol-class";
    case SymbolKind.Method:
      return "codicon:symbol-method";
    case SymbolKind.Property:
      return "codicon:symbol-property";
    case SymbolKind.Field:
      return "codicon:symbol-field";
    case SymbolKind.Constructor:
      return "codicon:symbol-method";
    case SymbolKind.Enum:
      return "codicon:symbol-enum";
    case SymbolKind.Interface:
      return "codicon:symbol-interface";
    case SymbolKind.Function:
      return "codicon:symbol-method";
    case SymbolKind.Variable:
      return "codicon:symbol-variable";
    case SymbolKind.Constant:
      return "codicon:symbol-constant";
    case SymbolKind.String:
      return "codicon:symbol-string";
    case SymbolKind.Number:
      return "codicon:symbol-numeric";
    case SymbolKind.Boolean:
      return "codicon:symbol-boolean";
    case SymbolKind.Array:
      return "codicon:symbol-array";
    case SymbolKind.Object:
      return "codicon:symbol-namespace"; // 没有对应的，用这个替代
    case SymbolKind.Key:
      return "codicon:symbol-key";
    case SymbolKind.Null:
      return "codicon:symbol-variable"; // 没有对应的，用这个替代
    case SymbolKind.EnumMember:
      return "codicon:symbol-enum-member";
    case SymbolKind.Struct:
      return "codicon:symbol-structure";
    case SymbolKind.Event:
      return "codicon:symbol-event";
    case SymbolKind.Operator:
      return "codicon:symbol-operator";
    case SymbolKind.TypeParameter:
      return "codicon:symbol-parameter";
    default:
      return "codicon:symbol-misc";
  }
}

// 根据 SymbolKind 获取对应的 CSS 变量颜色
function getSymbolIconColor(symbolKind?: SymbolKind): string {
  switch (symbolKind) {
    case SymbolKind.File:
      return "var(--vscode-symbolIcon-fileForeground)";
    case SymbolKind.Module:
      return "var(--vscode-symbolIcon-moduleForeground)";
    case SymbolKind.Namespace:
      return "var(--vscode-symbolIcon-namespaceForeground)";
    case SymbolKind.Package:
      return "var(--vscode-symbolIcon-packageForeground)";
    case SymbolKind.Class:
      return "var(--vscode-symbolIcon-classForeground)";
    case SymbolKind.Method:
      return "var(--vscode-symbolIcon-methodForeground)";
    case SymbolKind.Property:
      return "var(--vscode-symbolIcon-propertyForeground)";
    case SymbolKind.Field:
      return "var(--vscode-symbolIcon-fieldForeground)";
    case SymbolKind.Constructor:
      return "var(--vscode-symbolIcon-constructorForeground)";
    case SymbolKind.Enum:
      return "var(--vscode-symbolIcon-enumeratorForeground)";
    case SymbolKind.Interface:
      return "var(--vscode-symbolIcon-interfaceForeground)";
    case SymbolKind.Function:
      return "var(--vscode-symbolIcon-functionForeground)";
    case SymbolKind.Variable:
      return "var(--vscode-symbolIcon-variableForeground)";
    case SymbolKind.Constant:
      return "var(--vscode-symbolIcon-constantForeground)";
    case SymbolKind.String:
      return "var(--vscode-symbolIcon-stringForeground)";
    case SymbolKind.Number:
      return "var(--vscode-symbolIcon-numberForeground)";
    case SymbolKind.Boolean:
      return "var(--vscode-symbolIcon-booleanForeground)";
    case SymbolKind.Array:
      return "var(--vscode-symbolIcon-arrayForeground)";
    case SymbolKind.Object:
      return "var(--vscode-symbolIcon-objectForeground)";
    case SymbolKind.Key:
      return "var(--vscode-symbolIcon-keyForeground)";
    case SymbolKind.Null:
      return "var(--vscode-symbolIcon-nullForeground)";
    case SymbolKind.EnumMember:
      return "var(--vscode-symbolIcon-enumeratorMemberForeground)";
    case SymbolKind.Struct:
      return "var(--vscode-symbolIcon-structForeground)";
    case SymbolKind.Event:
      return "var(--vscode-symbolIcon-eventForeground)";
    case SymbolKind.Operator:
      return "var(--vscode-symbolIcon-operatorForeground)";
    case SymbolKind.TypeParameter:
      return "var(--vscode-symbolIcon-typeParameterForeground)";
    default:
      return "var(--vscode-foreground)";
  }
}

function isValidColor(color: string) {
  // 如果输入不是字符串，返回 false
  if (typeof color !== "string") return false;

  // 去除空格
  color = color.trim();

  let div = null;
  // 创建一个临时 div 元素
  div = DOM.createElement("div");

  // 设置颜色
  div.style.color = color;

  const is = div.style.color !== "";

  div = null;
  // 如果颜色设置无效，会返回空字符串
  return is;
}

export const InlineCode = ({ c, pos }: { c: string; pos?: Position }) => {
  c = c.trim();
  const isColorTxt = isValidColor(c || "");

  const config = useVsEditorConfig(state => state.editorConfig);
  const { message } = useComposerTaskContext();
  const { isCurrentWorkspaceSession, getPathInfoByContent } = useComposerState();

  // 生成缓存键
  const cacheKey = useMemo(() => generateCacheKey(c, message.sessionId, pos), [c, message.sessionId, pos]);

  // 1. 优先从会话缓存状态中获取数据
  const sessionPathInfo = useMemo(() => getPathInfoByContent(cacheKey), [cacheKey, getPathInfoByContent]);

  // 2. 再从内存缓存中获取数据
  const cachedData = useMemo(() => getCachedData(cacheKey), [cacheKey]);

  // 3. 优先使用会话缓存数据，其次使用内存缓存数据
  const initialPathInfo = sessionPathInfo || cachedData;

  const [symbolKind, setSymbolKind] = useState<SymbolKind | undefined>(initialPathInfo?.symbolKind);
  const [hasPathInfo, setHasPathInfo] = useState<boolean>(!!initialPathInfo);
  const [pathInfo, setPathInfo] = useState<OutPutBlockCodePath | undefined>(initialPathInfo || undefined);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const isCodeCanClick = useMemo(() => hasPathInfo && isCurrentWorkspaceSession, [hasPathInfo, isCurrentWorkspaceSession]);

  const onClick = useCallback(() => {
    kwaiPilotBridgeAPI.copyToClipboard(c);
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已复制",
    });
  }, [c]);

  const openFile = useCallback(async () => {
    if (!hasPathInfo || !pathInfo) return; // 如果没有路径信息，直接返回
    const { filepath, range } = pathInfo;
    kwaiPilotBridgeAPI.editor.openSymbolInFile({
      filepath,
      symbolText: c,
      range,
    });
  }, [c, hasPathInfo, pathInfo]);

  const renderSymbolIcon = useCallback(() => {
    // 如果正在加载，显示加载图标
    if (isLoading) {
      return (
        <Icon
          icon="codicon:loading"
          className="symbol-icon animate-spin"
        />
      );
    }

    if (!isCodeCanClick) return null;

    if (symbolKind === undefined) {
      return (
        <Icon
          icon="codicon:symbol-keyword"
          className="symbol-icon"
        />
      );
    }

    return (
      <Icon
        icon={getSymbolIcon(symbolKind)}
        className="symbol-icon"
        style={{ color: getSymbolIconColor(symbolKind) }}
      />
    );
  }, [isCodeCanClick, symbolKind, isLoading]);

  // 当会话缓存数据变化时，更新状态
  useEffect(() => {
    if (sessionPathInfo) {
      setSymbolKind(sessionPathInfo.symbolKind);
      setPathInfo(sessionPathInfo);
      setHasPathInfo(true);
      setIsLoading(false);
    }
  }, [sessionPathInfo]);

  useEffect(() => {
    if (isColorTxt) return;

    // 1. 如果已经有会话缓存数据，不需要重新请求
    if (sessionPathInfo) return;

    // 2. 如果已经有内存缓存数据，不需要重新请求
    if (cachedData !== null) return;

    // setIsLoading(true);
    console.log("解析", c);
    // 3. 调用后端服务
    kwaiPilotBridgeAPI.editor.getFilePathOfBlockCode(c, message.sessionId, pos).then((res: OutPutBlockCodePath | undefined) => {
      // 将结果存入内存缓存
      setCachedData(cacheKey, res);
      if (res?.symbolKind !== undefined) {
        setSymbolKind(res.symbolKind);
      }
      // 设置路径信息和是否有路径信息
      setPathInfo(res);
      setHasPathInfo(!!res);
      setIsLoading(false);
    }).catch((error) => {
      console.error("获取文件路径失败:", error);
      // 缓存失败结果，避免重复请求
      setCachedData(cacheKey, undefined);
      setPathInfo(undefined);
      setHasPathInfo(false);
      setIsLoading(false);
    });
  }, [c, isColorTxt, message.sessionId, pos, cacheKey, cachedData, sessionPathInfo]);

  if (isColorTxt) {
    return (
      <span
        className="kwaipilot-inline-code inline-flex items-center px-1 rounded border-[0.6px] cursor-pointer border-[#4F565F] h-[20px] align-middle  w-fit mx-1 truncate max-w-full whitespace-nowrap hover:bg-[var(--vscode-list-hoverBackground)]"
        style={{
          fontFamily: config.fontFamily,
        }}
        onClick={onClick}
      >
        <span
          className="size-[12px] rounded-sm mr-1"
          style={{
            background: c,
          }}
        >
        </span>
        <span className="truncate whitespace-nowrap align-middle leading-[18px]">
          {c}
        </span>
      </span>
    );
  }

  return (
    <span
      className={`kwaipilot-inline-code ${
        isCodeCanClick ? "chat-inline-anchor-widget" : "chat-inline-normal-widget"
      }`}
      style={{
        fontFamily: config.fontFamily,
      }}
      onClick={isCodeCanClick ? openFile : undefined}
    >
      {renderSymbolIcon()}
      <AutoTooltip title={c} className="symbol-label">
        {c}
      </AutoTooltip>
    </span>
  );
};

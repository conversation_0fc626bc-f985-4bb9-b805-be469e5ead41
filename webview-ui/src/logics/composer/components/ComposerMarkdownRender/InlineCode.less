.kwaipilot-inline-code {
    display: inline-flex;
    align-items: center;
    color: var(--vscode-textPreformat-foreground);
    line-height: 15px;
    margin: 0 1px;
    padding: 1px 3px;
    text-wrap: nowrap;
    width: fit-content;
    font-weight: 400;
    border-radius: 4px;
    font-size: 12px;
    font-family: "SF Mono", Monaco, Menlo, Courier, monospace;

    &.chat-inline-anchor-widget {
        border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
        text-decoration: none;
        cursor: pointer;
        color: inherit;

        &:hover {
            color: var(--vscode-textLink-activeForeground);
            user-select: text;
            background-color: var(--vscode-list-hoverBackground);
        }

        .symbol-icon {
            vertical-align: middle;
            line-height: 1em;
            font-size: 12px;
            overflow: hidden;
        }

        .symbol-label {
            padding: 0 3px;
        }
    }

    &.chat-inline-normal-widget {
        color: var(--vscode-textPreformat-foreground);
        background-color: var(--vscode-textPreformat-background);
        white-space: pre-wrap;
    }
}
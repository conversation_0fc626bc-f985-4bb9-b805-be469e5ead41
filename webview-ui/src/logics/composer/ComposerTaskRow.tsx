import { Terminal } from "./tools/Terminal";
import { InternalLocalMessage, SayTool } from "shared/lib/agent";
import { ComposerTaskContextProvider, useComposerTaskContext } from "./context/ComposerTaskContext";
import { useCallback, useMemo } from "react";
import { EditFile } from "./tools/EditFile";
import { ComposerMarkdownRender } from "./components/ComposerMarkdownRender";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { GrepSearch } from "./tools/GrepSearch";
import { CodeBaseSearch } from "./tools/CodeBaseSearch";
import { ListFile } from "./tools/ListFile";
import { ReadFile } from "./tools/ReadFile";
import { ReadTask } from "./tools/ReadTask";
import { SearchMemory } from "./tools/SearchMemory";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Flex, FlexProps } from "@chakra-ui/react";
import { Mcp } from "./tools/Mcp";

export interface ComposerTaskRowProps extends FlexProps {
  message: InternalLocalMessage;
  isLast: boolean;
}

/**
 * 根据工具类型，判断渲染组件
 * @returns
 */
function ComposerTaskRowBody() {
  const { message } = useComposerTaskContext();

  // console.log("messageeeeeeeeeee:", message);

  const handleCopy = useCallback((content: string) => {
    reportUserAction({ key: "copy", type: "llmMsgCode" });
    kwaiPilotBridgeAPI.copyToClipboard(content.replace(/\n$/, ""));
    collectClick("VS_COPY_CODE");
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已复制",
    });
  }, []);

  const tool = useMemo(() => {
    if (message.ask === "tool" || message.say === "tool") {
      return JSON.parse(message.text || "{}") as SayTool;
    }
    return null;
  }, [message.ask, message.say, message.text]);

  if (tool) {
    switch (tool.tool) {
      case "grepSearch":
        return <GrepSearch tool={tool} />;
      case "codebaseSearch":
        return <CodeBaseSearch tool={tool} />;
      case "listFilesTopLevel":
      case "listFilesRecursive":
        return <ListFile tool={tool} />;
      case "readFile":
        return <ReadFile tool={tool} />;
      case "searchMemory":
        return <SearchMemory tool={tool} />;
      case "readTask":
        return <ReadTask tool={tool} />;
      case "editFile":
        return (
          <EditFile />
        );
    }
  }
  if (message.ask === "command" || message.say === "command") {
    return (
      <Terminal></Terminal>
    );
  }

  if (message.ask === "use_mcp_tool" || message.say === "use_mcp_tool_result") {
    return (
      <Mcp></Mcp>
    );
  }

  if (message.type === "say") {
    const messageSay = message.say;
    if (!messageSay) {
      return null;
    }
    switch (messageSay) {
      case "completion_result":
        return (
          <ComposerMarkdownRender content={message.text || ""} handleCopy={handleCopy} />
        );
      case "error":
        return (
          <ComposerMarkdownRender content={message.text || ""} handleCopy={handleCopy} />
        );
      case "text":
      case "api_req_finished":
      case "api_req_retried":
      case "api_req_started":
      case "checkpoint_created":
      case "command_output":
      case "edit_file_result":
      case "api_req_failed":
      case "tool_error":
      case "task":
      case "tool":
      case "user_feedback":
        break;
      default: {
        // 用于类型校验
        const sayValue: never = messageSay;
        console.warn("unknown say", sayValue);
        break;
      }
    }
  }
  else if (message.type === "ask") {
    // xxx
  }
  return (
    <ComposerMarkdownRender content={message.text || ""} handleCopy={handleCopy} />
  );
}

export function ComposerTaskRow({ message, isLast, ...rest }: ComposerTaskRowProps) {
  const shouldRender = useMemo(() => {
    if (message.type === "say" && message.say === "tool_error") {
      return false;
    }
    return true;
  }, [message.type, message.say]);

  const isToolUse = useMemo(() => {
    if (
      message.ask === "tool" || message.say === "tool" || message.ask === "command" || message.say === "command" || message.ask === "use_mcp_tool" || message.say === "use_mcp_tool_result"
    ) {
      return true;
    }
    return false;
  }, [message.ask, message.say]);
  return (
    <ComposerTaskContextProvider message={message} isLast={isLast}>
      {shouldRender && (
        <Flex flexDirection="column" paddingBottom={isToolUse ? "2" : ""} paddingTop={isToolUse ? "2" : ""} {...rest}>
          <ComposerTaskRowBody />
        </Flex>
      )}
    </ComposerTaskContextProvider>
  );
}

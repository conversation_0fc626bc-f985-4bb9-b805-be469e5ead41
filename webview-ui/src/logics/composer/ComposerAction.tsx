import { SingleIcon } from "@/components/SingleIcon";
import DislikeIcon from "@/assets/dislike.svg?react";
import { useCallback, useMemo, useState } from "react";
import CopyIcon from "@/assets/copy.svg?react";
import LikeIcon from "@/assets/like.svg?react";
import LineIcon from "@/assets/line.svg?react";
import { useComposerState } from "./context/ComposerStateContext";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { InternalLocalMessage } from "shared/lib/agent/types";
import { useComposerConversationContext } from "./context/ComposerConversationContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Tooltip } from "@/components/Union/chakra-ui";
import { BoxProps, chakra, Flex } from "@chakra-ui/react";

import { Icon } from "@/components/Union/t-iconify";
import { DOM } from "@/utils/dom";
import { createPlainTextEditorState } from "@/components/TextArea/lexical/editorState";
import { generateCustomUUID } from "@/utils/sessionUtils";

interface IProps extends BoxProps {
  taskRows: InternalLocalMessage[];
  isLast: boolean;
}

export const Action: React.FC<IProps> = (props) => {
  const { taskRows, isLast, ...rest } = props;
  const { localMessages: messages } = useComposerState();
  const sessionId = messages[0].sessionId;

  const [likeStatus, setLikeStatus] = useState<"like" | "unlike" | "cancel">("cancel");

  const lickIconClick = useCallback(() => {
    setLikeStatus((state) => {
      const res = state === "like" ? "cancel" : "like";
      if (res === "like") {
        collectClick("VS_LIKE_BUTTON");
        const parms: ReportOpt<"like"> = {
          key: "like",
          type: undefined,
        };
        reportUserAction(parms, sessionId);
      }
      return res;
    });
  }, [sessionId]);

  const onLikeOrUnlike = useCallback(() => {
    setLikeStatus((state) => {
      const res = state === "unlike" ? "cancel" : "unlike";
      if (res === "unlike") {
        collectClick("VS_UNLIKE_BUTTON");
        const parms: ReportOpt<"dislike"> = {
          key: "dislike",
          type: undefined,
        };
        reportUserAction(parms, sessionId);
      }
      return res;
    });
  }, [sessionId]);

  const onContinue = useCallback(async () => {
    const para: ReportOpt<"agent_overlength_click"> = {
      key: "agent_overlength_click",
      type: undefined,
    };
    reportUserAction(para);

    const CONTINUE_PROMPT = "继续";

    const questionForHumanReading = CONTINUE_PROMPT;

    const targetSessionId
      = sessionId || messages[0]?.sessionId || generateCustomUUID();

    // 立即滚动到底部
    const dialogContainer = DOM.$(".chat-dialog-container");
    if (dialogContainer) {
      dialogContainer.scrollTo({
        top: 0,
        behavior: "instant",
      });
    }

    const conversationId = generateCustomUUID();

    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "newTask",
      // 这个字段其实已经没用了
      task: questionForHumanReading,
      reqData: {
        sessionId: targetSessionId,
        chatId: conversationId,
      },
      rules: [],
      editorState: createPlainTextEditorState(CONTINUE_PROMPT),
      questionForHumanReading: questionForHumanReading,
      contextItems: [],
      editingMessageTs: undefined,
    });

    return true;

    // 发送给extension
  }, [sessionId, messages]);

  const showContinueButton = useMemo(() => {
    const lastMessage = taskRows.at(-1);
    return isLast && lastMessage && lastMessage.say === "completion_result" && lastMessage.stopReason === "result_token_isTooLong";
  }, [isLast, taskRows]);

  const onCopy = useCallback(() => {
    let result = "";
    taskRows.forEach((message) => {
      if (message.ask === "tool" || message.say === "tool") {
        const tool = JSON.parse(message.text || "{}");
        switch (tool.tool) {
          case "readFile":
            result += `文件查看: ${tool.path}\n`;
            break;
          case "codebaseSearch":
            result += `代码搜索: ${tool.query}\n`;
            break;
          case "listFilesTopLevel":
          case "listFilesRecursive":
            result += `文件列表查询: ${tool.path}\n`;
            break;
          case "grepSearch":
            result += `正则搜索: ${tool.regex}\n`;
            break;
          case "editFile":
            result += "写入文件\n";
            break;
        }
      }
      else {
        if (message.ask === "command" || message.say === "command") {
          result += "命令行执行\n";
        }
        else {
          if (message.text) {
            result += message.text + "\n";
          }
        }
      }
    });
    kwaiPilotBridgeAPI.copyToClipboard(result);
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已复制",
    });
  }, [taskRows]);

  return (
    <Flex justify="space-between" mt={3} pr={2} {...rest}>
      {showContinueButton
        ? (
            <Tooltip label="输出过长，继续获取更多结果" openDelay={500} placement="bottom-start">
              <chakra.button
                className="text-foreground text-[12px] px-1 h-[26px] flex items-center gap-1 rounded bg-transparent border border-solid border-commandCenter-inactiveBorder hover:bg-toolbar-hoverBackground"
                onClick={onContinue}
              >
                <Icon icon="solar:restart-outline" width="16" height="16" />
                <span className="whitespace-nowrap">继续生成</span>
              </chakra.button>
            </Tooltip>
          )
        : null}
      <div className="flex gap-[6px] ml-auto">
        <SingleIcon
          title="喜欢"
          active={likeStatus === "like"}
          onClick={lickIconClick}
        >
          <LikeIcon className="translate-y-[-1px] text-icon-foreground" />
        </SingleIcon>
        <SingleIcon
          title="不喜欢"
          active={likeStatus === "unlike"}
          onClick={onLikeOrUnlike}
        >
          <DislikeIcon className="translate-y-[1px] text-icon-foreground" />
        </SingleIcon>
        <div className="flex items-center text-border-dropdown-border">
          <LineIcon />
        </div>
        <SingleIcon onClick={onCopy} title="复制">
          <CopyIcon className="text-icon-foreground" />
        </SingleIcon>
      </div>
    </Flex>
  );
};

export const ComposerAction: React.FC<IProps & { isLast: boolean }> = (props) => {
  const { taskRows, isLast, ...rest } = props;
  const { isFinished } = useComposerConversationContext();
  const { isStreaming } = useComposerState();

  if (!isFinished || (isStreaming && isLast)) {
    return null;
  }

  return <Action taskRows={taskRows} isLast={isLast} {...rest}></Action>;
};

import { SayTool } from "shared/lib/agent";
import { Container } from "./components/Container";
import { Icon } from "./components/Icon";
import { Title } from "./components/Title";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { Loading } from "./components/loading";
import { SubTitle } from "./components/SubTitle";
import { Error } from "./components/Error";

interface IProps {
  tool: SayTool;

}

export const SearchMemory = (props: IProps) => {
  const tool = props.tool;

  const { isToolExecuting, isToolError } = useComposerTaskContext();

  // const openFile = useCallback(() => {
  //   if (tool.path) {
  //     if (!shouldReadEntireFile && startLine && endLine) {
  //       kwaiPilotBridgeAPI.editor.openFileToEditor(tool.path, startLine, endLine);
  //     }
  //     else {
  //       kwaiPilotBridgeAPI.editor.openFileToEditor(tool.path);
  //     }
  //   }
  // }, [tool.path, shouldReadEntireFile, startLine, endLine]);

  return (
    <Container className="hover:bg-list-hoverBackground cursor-pointer">
      <Icon type="read"></Icon>
      <Title type={tool.tool}></Title>
      {isToolExecuting ? <Loading></Loading> : isToolError ? <Error></Error> : tool.path && <SubTitle content={tool.path}></SubTitle> }
    </Container>
  );
};

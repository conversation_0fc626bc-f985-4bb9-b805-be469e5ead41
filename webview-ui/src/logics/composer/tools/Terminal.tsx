import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { useDesignToken } from "@/hooks/useDesignToken";
import { Tooltip } from "@/components/Union/chakra-ui";
import { Box, Button, Flex, Link, Show, Spinner } from "@chakra-ui/react";

import { useCallback, useEffect, useState } from "react";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { COMMAND_OUTPUT_STRING, COMMAND_REQ_APP_STRING, TerminalTextStructure } from "shared/lib/agent";
import { isTerminalMessage } from "shared/lib/agent/isToolMessage";
import IconExternal from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_skip";
import KidIcon from "@/components/Union/kid";

import { useComposerState } from "../context/ComposerStateContext";
import clsx from "clsx";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { Icon } from "@/components/Union/t-iconify";
import { vsCss } from "shared/lib/vscodeToken/index";
import AutoTooltip from "@/components/AutoTooltip";
import { AutoRunSelect } from "./components/AutoRunSelect";
import TerminalBlock from "../components/TerminalBlock";

export function IconTerminal({ className }: { className: string }) {
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path d="M5.02929 5.828L2.38229 8.474L5.02929 11.12L4.32129 11.828L1.32129 8.828V8.12L4.32129 5.12L5.02929 5.828ZM12.0293 5.12L11.3213 5.828L13.9683 8.474L11.3213 11.12L12.0293 11.828L15.0293 8.828V8.12L12.0293 5.12ZM5.22929 13.25L6.12329 13.698L11.1233 3.698L10.2293 3.25L5.22929 13.25Z" fill="currentColor" />
    </svg>
  );
}

function IconPendingDot() {
  return (
    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle opacity="0.5" cx="4.78174" cy="4.23468" r="3" fill="#FFBB26" />
      <circle cx="4.78174" cy="4.23468" r="2" fill="#FFBB26" />
    </svg>
  );
}

const splitMessage = (text: string) => {
  const outputIndex = text.indexOf(COMMAND_OUTPUT_STRING);
  if (outputIndex === -1) {
    return { command: text, output: "" };
  }
  return {
    command: text.slice(0, outputIndex).trim(),
    output: text
      .slice(outputIndex + COMMAND_OUTPUT_STRING.length)
      .trim()
      .split("")
      .map((char) => {
        switch (char) {
          case "\t":
            return "→   ";
          case "\b":
            return "⌫";
          case "\f":
            return "⏏";
          case "\v":
            return "⇳";
          default:
            return char;
        }
      })
      .join(""),
  };
};

function TerminalFooter({
  handleEnableChange,
}: {
  handleEnableChange?: (enabled: boolean) => void;
}) {
  const { message } = useComposerTaskContext();
  if (!isTerminalMessage(message)) {
    throw new Error("message is not a terminal message");
  }

  const autoRunFailedTips = message.autoRunFailedReason
    ? message.autoRunFailedReason === "agentRequiredApproval"
      ? (
          <AutoTooltip label="该命令agent判断有风险，需手动确认" className=" text-descriptionForeground text-[12px]">
            该命令agent判断有风险，需手动确认
          </AutoTooltip>
        )
      : message.autoRunFailedReason === "excluded"
        ? (
            <AutoTooltip label="该命令前缀在黑名单设置内，无法自动执行" className=" text-descriptionForeground text-[12px]">

              该命令前缀在
              <Button
                variant="link"
                textDecoration="none"
                size="xs"
                color={vsCss.textLinkForeground}
                _active={{ color: vsCss.textLinkActiveForeground }}
                mx={1}
                onClick={() => kwaiPilotBridgeAPI.extensionSettings.$openSettings("function")}
              >

                黑名单设置
              </Button>
              内，无法自动执行
            </AutoTooltip>
          )
        : undefined
    : undefined;

  return (
    <div className="bg-editor-background py-2 px-3 flex items-center gap-2">
      {autoRunFailedTips}
      <AutoRunSelect handleEnableChange={handleEnableChange} />
    </div>
  );
}

export function Terminal() {
  const { tokens } = useDesignToken();
  const { message, isLast } = useComposerTaskContext();
  const { currentTaskInterrupted } = useComposerState();

  const [commandRunClicked, setCommandRunClicked] = useState(false);
  const [commandCancelClicked, setCommandCancelClicked] = useState(false);

  if (!isTerminalMessage(message)) {
    throw new Error("message is not a terminal message");
  }
  const terminalInfo = JSON.parse(message.text || "{}") as TerminalTextStructure;
  const { command: rawCommand, output } = splitMessage(terminalInfo.command || "");
  const requestsApproval = rawCommand.endsWith(COMMAND_REQ_APP_STRING);
  const command = requestsApproval ? rawCommand.slice(0, -COMMAND_REQ_APP_STRING.length) : rawCommand;

  // 添加状态来保存用户编辑后的命令内容
  const [editedCommand, setEditedCommand] = useState<string>("");
  const [hasBeenEdited, setHasBeenEdited] = useState<boolean>(false);

  // 使用编辑后的命令或原始命令
  const displayCommand = hasBeenEdited ? editedCommand : command;

  // 判断运行按钮是否应该禁用
  const isRunDisabled = hasBeenEdited ? !editedCommand.trim() : !command.trim();
  // 处理命令编辑
  const handleCommandChange = useCallback((newCommand: string) => {
    setEditedCommand(newCommand);
    setHasBeenEdited(true);
  }, []);

  /**
   * 点击运行终端
   */
  const onRunTerminalClick = useCallback(() => {
    // 发送用户编辑后的命令（如果有的话）
    const commandToRun = hasBeenEdited ? editedCommand : command;
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "yesButtonClicked",
      text: commandToRun,
    });
    setCommandRunClicked(true);
  }, [editedCommand, command, hasBeenEdited]);
  const onSkipClick = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "noButtonClicked",
      text: "",
    });
    setCommandCancelClicked(true);
  }, []);

  const onOpenTerminalClick = useCallback(() => {
    kwaiPilotBridgeAPI.composerToggleTerminal();
  }, []);

  // const outputReceived = useMemo(() => message?.text?.includes(COMMAND_OUTPUT_STRING), [message]);
  const isCommandExecuting = ("outputMessage" in message && message.outputMessage?.partial) && isLast && commandRunClicked;
  const canCommandProceededByUser = !currentTaskInterrupted && !commandRunClicked && !commandCancelClicked;

  useEffect(() => {
    if (canCommandProceededByUser && message.autoRun && !isRunDisabled) {
      onRunTerminalClick();
    }
  }, [canCommandProceededByUser, message.autoRun, onRunTerminalClick, isRunDisabled]);

  const handleEnableChange = useCallback((enabled: boolean) => {
    if (enabled && canCommandProceededByUser && !isRunDisabled) {
      // 自动执行
      onRunTerminalClick();
    }
  }, [canCommandProceededByUser, onRunTerminalClick, isRunDisabled]);

  return (
    <div className={clsx(" rounded border-[0.6px] border-radio-inactiveBorder")}>
      <div className=" flex items-center gap-1 leading-[18px] text-[13px] py-2 px-3 bg-statusBarItem-remoteHoverBackground">
        <IconTerminal className="flex-shrink-0 translate-y-[0.8px] size-[14px] text-icon-foreground border rounded-[3.5px] border-commandCenter-inactiveBorder" />
        <Show breakpoint="(min-width: 320px)">
          <span className="text-[13px] text-foreground">
            命令行
          </span>
        </Show>
        {message.partial
          ? (
              <Flex className=" text-text-common-disable text-[12px] whitespace-nowrap" align="center" gap={1}>
                <Spinner size="xs" color="inherit" />
                <span>正在生成</span>
              </Flex>
            )
          : isLast && !currentTaskInterrupted
            && !commandRunClicked && !commandCancelClicked && (
            <Tooltip label="等待用户操作" placement="top">
              <div className=" ml-1 whitespace-nowrap">
                <span title="等待操作" className=" text-text-common-disable flex items-center gap-1">
                  <IconPendingDot />
                </span>
              </div>
            </Tooltip>
          )}

        {!message.partial && isLast && (
          <Flex gap={2} ml="auto" align="center" flex="none">
            <Tooltip label="打开终端" placement="top">
              <Link

                textDecoration="none"
                fontSize={12}
                onClick={onOpenTerminalClick}
                gap={1}
                color={tokens.colorTextCommonSecondary}
                flexWrap="nowrap"
                display="flex"
                alignItems="center"
              >
                <KidIcon config={IconExternal} size={14} color="inherit" />

              </Link>
            </Tooltip>
            {canCommandProceededByUser && (
              <>
                <div className=" h-[12px] w-[1px] bg-border-common rounded-sm"></div>
                <Link textDecoration="none" fontSize={12} onClick={onSkipClick} color={tokens.colorTextCommonSecondary}>
                  取消
                </Link>
                <button
                  className="bg-button-background hover:bg-button-hoverBackground text-[12px] font-medium flex items-center gap-1 text-button-foreground leading-[18px] py-[2px] px-1 rounded"
                  onClick={onRunTerminalClick}
                  disabled={isRunDisabled}
                  style={
                    isRunDisabled
                      ? { cursor: "not-allowed", opacity: 0.5 }
                      : {}
                  }
                >
                  <Icon icon="si:play-fill" className="text-[#E5ECF2]" />
                  <span>运行</span>
                </button>
              </>
            ) }
            {isCommandExecuting
            && (
              <button
                className="bg-button-background hover:bg-button-hoverBackground text-[12px] font-medium flex items-center gap-1 text-button-foreground leading-[18px] py-[2px] px-1 rounded"
                onClick={onRunTerminalClick}
              >
                <Spinner size="xs"></Spinner>
                <span>运行</span>
              </button>
            )}
          </Flex>
        )}
      </div>
      <div>

        <CustomScrollBar className=" px-2 box-border py-2 overflow-auto max-w-full">
          <TerminalBlock
            language="shellscript"
            source={displayCommand}
            forceWrap={true}
            clearPrebg={true}
            className="w-max"
            canEdit={!message.partial && isLast && canCommandProceededByUser}
            onSourceChange={handleCommandChange}
          >
          </TerminalBlock>
        </CustomScrollBar>
      </div>
      {terminalInfo.isBackground
        ? (
            <div className=" text-text-common-disable text-[12px] px-3 py-2">
              识别到为长时间命令，前往 vscode 终端面板观察日志
            </div>
          )
        : (
            <Box as="pre" px={3} py={2} fontSize="12px" lineHeight="18px" whiteSpace="pre-wrap">
              {output}
            </Box>
          )}
      <TerminalFooter handleEnableChange={handleEnableChange} />

    </div>
  );
}

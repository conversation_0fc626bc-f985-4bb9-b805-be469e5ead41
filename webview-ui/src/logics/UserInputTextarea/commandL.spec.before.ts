import { kwaiPilotBridgeAPI } from "@/bridge";
import { of } from "rxjs";
import { Mock } from "vitest";

(kwaiPilotBridgeAPI.observableAPI.visibility as Mock).mockReturnValue(of(true));
(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection as Mock).mockReturnValue(of(null));
(kwaiPilotBridgeAPI.observableAPI.userInfo as Mock).mockReturnValue(of(null));
(kwaiPilotBridgeAPI.observableAPI.settingUpdate as Mock).mockReturnValue(of({}));

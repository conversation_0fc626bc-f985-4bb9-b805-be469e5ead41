import { useMemo } from "react";
import { RichEditorBoxPanelData } from "shared/lib/richEditor/const";
import { CommandPrefix, PromptConfig, SharpCommand, SlashCommand } from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { warnNeverType } from "@/utils/throwUnknownError";
import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Knowledge, MentionNodeV2Structure_Rule, MentionNodeV2Structure_SlashCommand, MentionNodeV2Structure_Tree, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";
import { CustomPromptData } from "shared/lib/CustomVariable";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { basename } from "path-browserify";
import { getSortFileOrDir } from "@/components/TextArea/utils";
import { useRecentUsedPrompts } from "@/components/TextArea/components/NewPanel/useRecentUsedPrompts";
import { usePromptTemplate } from "@/store/promptTemplate";
import { SelectionOrFileContext } from "shared/lib/bridge/protocol-observable";
import { Doc } from "shared/lib/business";
import { useIdeEnv } from "@/hooks/useIdeEnv";

export enum TypeaheadMenuOptionType {
  /** 带子菜单的 */
  heading = "heading",
  /** 文件 */
  file = "file",
  /** 文件夹 */
  folder = "folder",
  /** 项目规则 */
  rule = "rule",
  /** 添加新的规则 */
  addRule = "addRule",
  /** 初始状态一级面板 */
  none = "none",
  /** 头部 */
  header = "header",
  /** 自定义指令 */
  customPrompt = "customPrompt",
  /** 快捷指令 */
  slashCommand = "slashCommand",
  /** flag 开启联网 */
  web = "web",
  /** 对话模式代码库 codebase */
  codebase = "codebase",
  /** 对话模式知识库 knowledge */
  knowledge = "knowledge",
}

export class MentionTypeaheadOption<T extends TypeaheadMenuOptionType = TypeaheadMenuOptionType > extends MenuOption {
  type: T;
  name: string;
  secondaryText: string;
  disabled: boolean = false;

  structure: T extends TypeaheadMenuOptionType.file
    ? MentionNodeV2Structure_File
    : T extends TypeaheadMenuOptionType.folder
      ? MentionNodeV2Structure_Tree
      : T extends TypeaheadMenuOptionType.customPrompt
        ? CustomPromptData
        : T extends TypeaheadMenuOptionType.rule
          ? MentionNodeV2Structure_Rule
          : T extends TypeaheadMenuOptionType.addRule
            ? undefined
            : T extends TypeaheadMenuOptionType.web
              ? null
              : T extends TypeaheadMenuOptionType.knowledge
                ? MentionNodeV2Structure_Knowledge
                : T extends TypeaheadMenuOptionType.slashCommand
                  ? MentionNodeV2Structure_SlashCommand
                  : null;

  constructor(type: T, name: string, secondaryText: string, structure: MentionTypeaheadOption<T>["structure"]) {
    super(name);
    this.type = type;
    this.name = name;
    this.secondaryText = secondaryText;
    this.structure = structure;
  }
}

const getDefaultMentionOptions = ({
  mode,
}: {
  mode: "chat" | "composer";
}) => {
  const options: MentionTypeaheadOption<any>[] = [
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.file, "文件", null),
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.folder, "目录", null),
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.knowledge, "知识库", null) : undefined,
    new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.rule, "规则", null),
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.codebase, "", "", null) : undefined,
    mode === "chat" ? new MentionTypeaheadOption(TypeaheadMenuOptionType.web, "", "", null) : undefined,
  ].filter((v): v is MentionTypeaheadOption<any> => Boolean(v));
  return options;
};

function BoxPanelData2MentionTypeaheadOption(data: RichEditorBoxPanelData): MentionTypeaheadOption {
  return data.key === SharpCommand.FOLDER
    ? new MentionTypeaheadOption<TypeaheadMenuOptionType.folder>(
      TypeaheadMenuOptionType.folder,
      data.title,
      data.description,
      {
        type: "tree",
        uri: data.uri,
        relativePath: data.data,
      },
    )
    : data.key === SharpCommand.RULES
      ? new MentionTypeaheadOption<TypeaheadMenuOptionType.rule>(
        TypeaheadMenuOptionType.rule,
        data.title,
        data.description,
        {
          type: "rule",
          uri: data.uri,
          relativePath: data.data,
        },
      )
      : new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
        TypeaheadMenuOptionType.file,
        data.title,
        data.description,
        {
          type: "file",
          uri: data.uri,
          relativePath: data.data,
        },
      );
}

export function useMentionOptions({ currentMenu,
  queryString,
  mode,
}: {
  currentMenu: TypeaheadMenuOptionType;
  queryString: string;
  /**
   * 对话模式和智能体模式的不同
   *
   * * 对话模式有代码库\知识库\自定义指令
   */
  mode: "chat" | "composer";
}) {
  const codeSearchDefaultDir = useRichEditPanelMenuStore(state => state.codeSearchDefaultDir);
  const codeSearchDefaultFiles = useRichEditPanelMenuStore(state => state.codeSearchDefaultFiles);
  const codeSearchWorkspaceDir = useRichEditPanelMenuStore(state => state.codeSearchWorkspaceDir);
  const codeSearchWorkspaceFiles = useRichEditPanelMenuStore(state => state.codeSearchWorkspaceFiles);
  const ruleFiles = useRichEditPanelMenuStore(state => state.ruleFiles);

  const disabledMenu = useRichEditPanelMenuStore(state => state.disabledMenu);
  const knowledgeList = useRichEditPanelMenuStore(state => state.docList);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");

  const options = useMemo<MentionTypeaheadOption[]>(() => {
    if ((queryString === null || queryString === "") && currentMenu === TypeaheadMenuOptionType.none) {
      return getDefaultMentionOptions({ mode });
    }
    else if (currentMenu === TypeaheadMenuOptionType.none) {
      type DocOrBoxPanelDataSearch = { doc: Doc; title: string } | { boxPanelData: RichEditorBoxPanelData; title: string };
      // 处理带 query 的情况
      const searchCandidates: DocOrBoxPanelDataSearch[] = [];
      if (!disabledMenu[SharpCommand.FOLDER].status) {
        searchCandidates.push(...codeSearchWorkspaceDir.map(v => ({ boxPanelData: v, title: v.title })));
      }
      searchCandidates.push(
        ...codeSearchWorkspaceFiles.map(v => ({ boxPanelData: v, title: v.title })),
        ...ruleFiles.map(v => ({ boxPanelData: v, title: v.title })),
      );
      if (mode === "chat") {
        searchCandidates.push(...knowledgeList.map(doc => ({ doc, title: doc.name })));
      }
      return getSortFileOrDir(searchCandidates, queryString)
        .map(searchItem => "boxPanelData" in searchItem
          ? BoxPanelData2MentionTypeaheadOption(searchItem.boxPanelData)
          : knownledgeDoc2MentionTypeaheadOption(searchItem.doc),
        );
    }
    else {
      switch (currentMenu) {
        case TypeaheadMenuOptionType.file: {
          if (!queryString) {
            let fileList = codeSearchDefaultFiles.map(BoxPanelData2MentionTypeaheadOption);
            if (currentFileAndSelection) {
              fileList = [
                new MentionTypeaheadOption<TypeaheadMenuOptionType.file>(
                  TypeaheadMenuOptionType.file,
                  basename(currentFileAndSelection.relativePath),
                  currentFileAndSelection.relativePath,
                  {
                    type: "file",
                    uri: currentFileAndSelection.uri,
                    relativePath: currentFileAndSelection.relativePath,
                  },
                ),
                ...fileList.filter(v => (v as MentionTypeaheadOption<TypeaheadMenuOptionType.file>).structure.uri !== currentFileAndSelection.uri),
              ];
            }
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "文件", "", null),
              ...fileList,
            ];
          }
          else {
            const rawList = codeSearchDefaultFiles || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "文件", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
        case TypeaheadMenuOptionType.folder: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "目录", "", null),
              ...codeSearchDefaultDir.map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
          else {
            const rawList = codeSearchDefaultDir || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "目录", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
        case TypeaheadMenuOptionType.knowledge: {
          const header = new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "知识库", "", null);
          if (!queryString) {
            return [
              header,
              ...knowledgeList.map(doc => new MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge>(
                TypeaheadMenuOptionType.knowledge,
                doc.name,
                "",
                {
                  type: "knowledge",
                  uri: `knowledge://${doc.id}`,
                  relativePath: "",
                  doc,
                },
              )),
            ];
          }
          else {
            return [
              header,
              ...knowledgeList
                .filter(doc => doc.name.toLowerCase().includes(queryString.toLowerCase()))
                .map(doc => knownledgeDoc2MentionTypeaheadOption(doc)),
            ];
          }
        }
        case TypeaheadMenuOptionType.rule: {
          if (!queryString) {
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "规则", "", null),
              ...ruleFiles.map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
          else {
            const rawList = ruleFiles || [];
            return [
              new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "规则", "", null),
              ...getSortFileOrDir(
                rawList,
                queryString,
              ).map(BoxPanelData2MentionTypeaheadOption),
            ];
          }
        }
      }
    }
    return [];
  }, [queryString, currentMenu, mode, disabledMenu, codeSearchWorkspaceFiles, ruleFiles, codeSearchWorkspaceDir, codeSearchDefaultFiles, currentFileAndSelection, codeSearchDefaultDir, knowledgeList]);
  return {
    options,
  };
}

interface SlashCommandStructure {
  title: string;
  key: SlashCommand;
  search: string[];

}

const slashCommands = [
  {
    title: "函数注释",
    key: SlashCommand.FUNC_COMMENT,
    search: [
      "函数注释",
      "hanshuzhushi",
      "hszs",
      "zs",
      "s",
      "注释",
      "释",
      "zhushi",
      "数",
    ],
  },
  {
    title: "行间注释",
    key: SlashCommand.LINE_CODE_COMMENT,
    search: [
      "行间注释",
      "hangjianzhushi",
      "hjzs",
      "zs",
      "s",
      "注释",
      "释",
      "zhushi",
      "间",
      "j",
    ],
  },
  {
    title: "代码解释",
    key: SlashCommand.CODE_EXPLAIN,
    search: [
      "代码解释",
      "daimajieshi",
      "dmjs",
      "js",
      "s",
      "解释",
      "释",
      "jieshi",
      "码",
      "m",
    ],
  },
  {
    title: "代码优化",
    key: SlashCommand.CODE_REFACTOR,
    search: [
      "代码优化",
      "daimayouhua",
      "dmyh",
      "yh",
      "h",
      "优化",
      "化",
      "youhua",
      "码",
      "m",
    ],
  },
  {
    title: "函数拆分",
    key: SlashCommand.FUNC_SPLIT,
    search: [
      "函数拆分",
      "hanshuchaifen",
      "hscf",
      "cf",
      "f",
      "拆分",
      "分",
      "chaifen",
      "数",
      "s",
    ],
  },
  {
    title: "单元测试",
    key: SlashCommand.UNIT_TEST,
    search: [
      "单元测试",
      "danyuanceshi",
      "dycs",
      "cs",
      "s",
      "测试",
      "试",
      "ceshi",
      "元",
      "y",
    ],
  },

];

const customPromptCommand: SlashCommandStructure = {
  title: "自定义指令",

  key: SlashCommand.CUSTOM_PROMPT,
  search: [
    "自定义指令",
    "zidingyizhiling",
    "zdyzl",
    "zdzl",
    "zl",
    "自定义",
    "zidingyi",
    "zdy",
    "zdy",
    "指令",
    "zhiling",
    "zl",
  ],
};

const clearContextCommand: SlashCommandStructure = {
  title: "清除上下文",
  key: SlashCommand.CLEAR_CONVERSATION,
  search: [
    "清除上下文",
    "qingchushangxiawen",
    "qcsxw",
    "sxw",
    "w",
    "上下文",
    "清除",
    "shangxiawen",
    "qing",
    "qingchu",
  ],
};

function slashCommandStructure2MentionTypeaheadOption(
  structure: SlashCommandStructure,
  promptTemplates: PromptConfig[],
  currentFileAndSelection: SelectionOrFileContext | null | undefined,
): MentionTypeaheadOption<any> {
  return new MentionTypeaheadOption(
    TypeaheadMenuOptionType.slashCommand, "", "",
    {
      type: "slashCommand",
      uri: `slashCommand://${structure.key as SlashCommand}`,
      command: structure.key,
      relativePath: "",
      label: structure.title,
      template: promptTemplates.find(v => structure.key === CommandPrefix.SLASH + v.key)?.template || "",
      contextItem: slashCommandSetRequiringContextItem.has(structure.key)
        ? currentFileAndSelection?.range
          ? {
              type: "selection",
              content: currentFileAndSelection.content,
              uri: currentFileAndSelection.uri,
              relativePath: currentFileAndSelection.relativePath,
              range: currentFileAndSelection.range,
            }
          : currentFileAndSelection
            ? {
                type: "file",
                uri: currentFileAndSelection.uri || "",
                relativePath: currentFileAndSelection.relativePath || "",
              }
            : undefined
        : undefined,
    });
}

function slashCommandMatchQuery(structure: SlashCommandStructure, queryString: string): boolean {
  return structure.search?.some(searchItem =>
    searchItem.toLowerCase().startsWith(queryString.toLowerCase()),
  );
}

export function useSlashOptions({
  currentMenu,
  queryString,
}: {

  currentMenu: TypeaheadMenuOptionType;
  queryString: string;
  /**
   * 对话模式和智能体模式的不同
   *
   * * 对话模式有代码库\知识库\自定义指令
   */
  mode: "chat" | "composer";
}) {
  const customPrompts = useRichEditPanelMenuStore(state => state.customPrompts);

  const promptTemplate = usePromptTemplate(s => s.promptTemplate);

  const { recentUsedPrompts } = useRecentUsedPrompts();
  const recentUsedPromptIds = useMemo(() => {
    return recentUsedPrompts.map(item => item.id);
  }, [recentUsedPrompts]);

  const currentFileAndSelection = useBridgeObservableAPI("currentFileAndSelection");
  const [, isKwaiPilotIDE] = useIdeEnv();
  const options = useMemo<MentionTypeaheadOption[]>(() => {
    if ((queryString === null || queryString === "") && currentMenu === TypeaheadMenuOptionType.none) {
      const header = new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "指令", "", null);
      // 第一层的元素不能返回
      header.disabled = true;
      const options: MentionTypeaheadOption<any>[] = [
        header,
        ...slashCommands.map(item => slashCommandStructure2MentionTypeaheadOption(item, promptTemplate, currentFileAndSelection)),
        !isKwaiPilotIDE && new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.customPrompt, "", null),
        slashCommandStructure2MentionTypeaheadOption(clearContextCommand, promptTemplate, currentFileAndSelection),
      ].filter((v): v is MentionTypeaheadOption<any> => Boolean(v));
      return options;
    }
    else if (currentMenu === TypeaheadMenuOptionType.none) {
      const options = slashCommands
        .filter(v => slashCommandMatchQuery(v, queryString))
        .map(v => slashCommandStructure2MentionTypeaheadOption(v, promptTemplate, currentFileAndSelection));
      if (slashCommandMatchQuery(customPromptCommand, queryString)) {
        options.push(new MentionTypeaheadOption(TypeaheadMenuOptionType.heading, TypeaheadMenuOptionType.customPrompt, "", null));
      }
      if (slashCommandMatchQuery(clearContextCommand, queryString)) {
        options.push(slashCommandStructure2MentionTypeaheadOption(clearContextCommand, promptTemplate, currentFileAndSelection));
      }
      return options;
    }
    else {
      switch (currentMenu) {
        case TypeaheadMenuOptionType.customPrompt: {
          const customPromptsHitResult = getSortFileOrDir(customPrompts, queryString).map(
            prompt =>
              new MentionTypeaheadOption(
                TypeaheadMenuOptionType.customPrompt,
                prompt.data,
                prompt.description,
                prompt.raw,
              ),
          );

          const recentUsedPrompts = customPromptsHitResult.filter(item =>
            recentUsedPromptIds.includes(item.structure.id),
          );
          const restPrompts = customPromptsHitResult.filter(
            item => !recentUsedPromptIds.includes(item.structure.id),
          );

          return [
            new MentionTypeaheadOption(TypeaheadMenuOptionType.header, "自定义指令", "", null),
            ...recentUsedPrompts,
            ...restPrompts,
          ];
        }
        case TypeaheadMenuOptionType.file:
        case TypeaheadMenuOptionType.addRule:
        case TypeaheadMenuOptionType.codebase:
        case TypeaheadMenuOptionType.header:
        case TypeaheadMenuOptionType.heading:
        case TypeaheadMenuOptionType.knowledge:
        case TypeaheadMenuOptionType.rule:
        case TypeaheadMenuOptionType.web:
        case TypeaheadMenuOptionType.slashCommand:
        case TypeaheadMenuOptionType.folder: {
          // 不处理
          return [];
        }
        default: {
          const neverType = currentMenu;
          warnNeverType(neverType);
          return [];
        }
      }
    }
  }, [currentFileAndSelection, currentMenu, customPrompts, promptTemplate, queryString, recentUsedPromptIds, isKwaiPilotIDE]);
  return {
    options,
  };
}
function knownledgeDoc2MentionTypeaheadOption(doc: Doc): MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge> {
  return new MentionTypeaheadOption<TypeaheadMenuOptionType.knowledge>(
    TypeaheadMenuOptionType.knowledge,
    doc.name,
    "",
    {
      type: "knowledge",
      uri: `knowledge://${doc.id}`,
      relativePath: "",
      doc,
    },
  );
}

import { getRecordStoreByVendor, selectActiveModel, useRecordStore } from "@/store/record";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import { useCallback } from "react";
import { UserInputTextareaProps } from "../UserInputTextarea/UserInputTextArea";
import { chatId } from "@/utils/chatId";
import { findFirst } from "@/components/TextArea/lexical/traversal";
import { GeneratePromptFile, SlashCommand } from "@shared/types";
import { transformToPlainTextForHumanReading } from "@/components/TextArea/lexical/editorState";
import { ICachedMessageQuestionV2, QAItem } from "@shared/types/chatHistory";
import { DEFAULT_MODEL_TYPE, ext2LanguageId } from "@/constant";
import { generateCustomUUID } from "@/utils/sessionUtils";
import { getSlashCommandReportType, removeCustomHtmlBlock } from "@/components/TextArea/utils";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { httpClient } from "@/http";
import repoChatService from "@/services/repo-chat";
import { useUserStore } from "@/store/user";
import { getCurrentSessionTimeString } from "@/utils/utils";
import { fetchSummaryConversation } from "@/http/api/summaryConversation";
import { useHistoryStore } from "@/store/history";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import { logger } from "@/utils/logger";
import { produce } from "immer";
import { collectContextLog } from "../composer/useSubmit";
import { MessageType } from "@/utils/const";
import { postMessageUtil } from "@/utils/postMessageUtil";
import { isMentionNodeV2, MentionNodeV2Structure_SlashCommand, SerializedMentionNodeV2 } from "shared/lib/MentionNodeV2/nodes";
import { URI } from "vscode-uri";
import { useAsync } from "react-use";
import pTimeout from "p-timeout";
import { isIdentical } from "../UserInputTextarea/ContextHeader/ContextHeaderContext";

export function useChatSubmit() {
  const clearSuggestQuestion = useRecordStore(
    state => state.clearSuggestQuestion,
  );

  const chatModel = useRecordStore(selectActiveModel);
  const activeSession = useRecordStore(state => state.activeSession);
  const sessionHistory = useRecordStore(state => state.sessionHistory);
  const setSessionHistory = useRecordStore(state => state.setSessionHistory);
  const abortCurrentChat = useRecordStore(state => state.abortCurrentChat);
  const setActiveSession = useRecordStore(state => state.setActiveSession);
  const userInfo = useUserStore(state => state.userInfo);
  const updateHistoryList = useHistoryStore(state => state.updateHistoryList);
  const navigate = useNavigateWithCache();
  const setLoadingStatu = useRecordStore(state => state.setLoadingStatu);
  const finishLoading = useRecordStore(state => state.finishLoading);

  const summaryConversation = useRecordStore(
    state => state.summaryConversation,
  );

  const { value: workspaceInfo } = useAsync(() => kwaiPilotBridgeAPI.getWorkspaceUri());

  const doSubmit = useCallback<UserInputTextareaProps["doSubmit"]>(async ({
    editor,
    contextHeaderState,
  }) => {
    collectClick("VS_SUBMIT_BUTTON");
    const params: ReportOpt<"chat"> = {
      key: "chat",
      type: "new-chat",
    };

    if (!userInfo) {
      return { result: false };
    }

    const startTime = Date.now();

    clearSuggestQuestion();

    let sessionId = activeSession;
    const uniqueId
      = Date.now().toString(36) + Math.random().toString(36).substr(2);
    chatId.updateChatId(uniqueId);

    reportUserAction(params);

    // 在修改编辑器状态前先保存原始状态，确保包含所有sharp命令。slash命令会更新编辑器状态
    const originalEditorState = editor.getEditorState().toJSON();

    const slashCommand = findFirst(originalEditorState, (node): node is SerializedMentionNodeV2 => isMentionNodeV2(node) && node.structure.type === "slashCommand");
    const slashCommandStructure = slashCommand ? slashCommand.structure as MentionNodeV2Structure_SlashCommand : null;

    const questionForHumanReading = transformToPlainTextForHumanReading(originalEditorState);

    // 把提问和回答消息放进数组用于展示
    const newCacheMessage: QAItem = {
      id: uniqueId,
      Q: {
        isSelf: true,
        id: uniqueId,
        question: questionForHumanReading,
        reply: "",
        modelType: chatModel.modelType || DEFAULT_MODEL_TYPE,
        v2: true,
        plainText: questionForHumanReading,
        editorState: originalEditorState, // 使用保存的原始状态，而不是可能被修改后的状态
        contextItems: contextHeaderState.nodes.map(node => node.structure),
      } satisfies ICachedMessageQuestionV2,
      A: [
        {
          isSelf: false,
          modelType: chatModel.modelType ?? DEFAULT_MODEL_TYPE,
          id: uniqueId,
          question: questionForHumanReading,
          reply: "",
          answerId: generateCustomUUID(),
          indexing: false,
          ttfb: 0,
          thinkInfo: undefined,
          finishReason: null,
        },
      ],
    };

    const contextStructures = contextHeaderState.nodes.map(v => v.structure);

    if (slashCommandStructure?.contextItem && !contextStructures.some(v => isIdentical(v, slashCommandStructure.contextItem!))) {
      contextStructures.push(slashCommandStructure.contextItem);
    }

    const haveCodebaseNode = contextHeaderState.nodes.some(v => v.structure.type === "codebase");

    const useCodebaseSearch = haveCodebaseNode && Boolean(workspaceInfo?.result);

    if (slashCommandStructure && slashCommandStructure.command === SlashCommand.CLEAR_CONVERSATION) {
      abortCurrentChat();
      if (sessionHistory) {
        const newClearContextIndex = [
          ...new Set([
            ...sessionHistory.clearContextIndex,
            sessionHistory.cachedMessages.length - 1,
          ]),
        ];
        setSessionHistory({
          ...sessionHistory,
          clearContextIndex: newClearContextIndex,
        });
        kwaiPilotBridgeAPI.updateSessionInfo({
          sessionId: sessionHistory.sessionId,
          expiredIndex: sessionHistory.expiredIndex,
          clearContextIndex: newClearContextIndex,
        });
      }
      return { result: true };
    }

    const refFiles = contextStructures
      .filter(v => v.type === "remoteFile")
      .map(i => Number(i.uploadInfo.id));

    const docId = contextStructures.find(v => v.type === "knowledge")?.doc.id || 0;

    const useSearch = contextStructures.some(v => v.type === "web");

    // 立即设置loading状态
    setLoadingStatu(uniqueId, "loading");

    try {
      const parsedRules = await pTimeout(kwaiPilotBridgeAPI.extensionToLoacl.$parseRules(
        contextHeaderState.nodes.map(v => v.structure).filter(v => v.type === "rule")
          .map(v => v.relativePath),
      ), {
        milliseconds: 3000,
        message: "获取规则超时",
      });
      let formatQuery = await kwaiPilotBridgeAPI.extensionComposer.$editorStateToContextualTextForLlm(
        originalEditorState,
        String(workspaceInfo?.result),
        contextHeaderState.nodes.map(v => v.structure),
      );

      // 立即更新会话历史以展示问题
      const newSessionTime = getCurrentSessionTimeString();
      if (sessionHistory) {
        sessionId = sessionHistory.sessionId;
        setSessionHistory({
          ...sessionHistory,
          cachedMessages: [
            ...(sessionHistory?.cachedMessages ?? []),
            newCacheMessage,
          ],
          sessionTime: newSessionTime,
        });
      }
      else {
      // 新建对话
        sessionId = generateCustomUUID();
        // 切换至该对话
        setActiveSession({
          value: sessionId,
          updateLocalStorage: !!userInfo,
          updateHistory: false, // 不需要从 sqlite拉取历史记录
        });
        // 初始化新对话历史消息
        const newSession = {
          sessionId,
          sessionName: summaryConversation ? "" : removeCustomHtmlBlock(questionForHumanReading),
          sessionTime: newSessionTime,
          cachedMessages: [newCacheMessage],
          expiredIndex: [],
          clearContextIndex: [],
          isComposer: false,
        };
        if (summaryConversation) {
          fetchSummaryConversation(questionForHumanReading, (chunk) => {
            updateHistoryList(sessionId, chunk, false);
          });
        }

        setSessionHistory(newSession);
        if (userInfo) {
          kwaiPilotBridgeAPI.addSession(newSession);
        }
      }
      navigate("/chat");

      const haveSharp = contextStructures.length;
      if (haveSharp) {
        let feeback = false;
        const treeStructures = contextStructures.filter(v => v.type === "tree");
        const useLocalCodeSearch = treeStructures.length !== 0 || useCodebaseSearch;
        if (useLocalCodeSearch) {
          const chatHistory: {
            role: "user" | "assistant";
            content: string;
          }[] = [];
          sessionHistory?.cachedMessages.forEach((i) => {
            chatHistory.push({
              role: "user",
              content: i.Q.question,
            });
            chatHistory.push({
              role: "assistant",
              content: i.A[0].reply ?? "",
            });
          });
          const { status, data, message }
          = await kwaiPilotBridgeAPI.agent.searchSearch({
            query: formatQuery,
            chatHistory: chatHistory ?? [],
            targetDirectory: treeStructures.map(v => v.relativePath),
            topK: 10,
          });
          if (status === "failed") {
            logger.error("search search failed", "code-search", {
              value: {
                message,
              },
            });
            feeback = true;
          }
          else if (!data) {
            feeback = true;
          }
          else {
            const { code_context_list: codeContentList } = data;

            const localCodeSearchFailed = await httpClient.getLocalCodeSearchPrompt({
              question: formatQuery,
              codeContextList: codeContentList,
              rules: parsedRules,
            });
            if (!localCodeSearchFailed) {
              feeback = true;
            }
            else {
              formatQuery = localCodeSearchFailed;
            }
          }
        }

        if (feeback || !useLocalCodeSearch) {
          const fileStructures = contextStructures.filter(v => v.type === "file");
          const files = await repoChatService.getSelectFileCode(fileStructures.map(v => URI.parse(v.uri).fsPath));
          const selections: GeneratePromptFile[] = contextStructures
            .filter(v => v.type === "selection")
            .map((v) => {
              const uriObj = URI.parse(v.uri);
              return {
                name: uriObj.fsPath + `:L${v.range.start.line}-L${v.range.end.line}`,
                code: v.content,
                language: ext2LanguageId[v.relativePath.split(".").pop() ?? ""] ?? "",
              };
            });
          const useInstantApplyMode
          = slashCommandStructure?.command !== SlashCommand.UNIT_TEST
          && slashCommandStructure?.command !== SlashCommand.CODE_EXPLAIN
          && treeStructures.length === 0 && !haveCodebaseNode;
          const prompt = await httpClient.getCodeSearchPrompt({
            files: [...files, ...selections],
            searchTargetDirs: treeStructures.map(v => v.relativePath),
            query: formatQuery,
            codebaseSearch: useCodebaseSearch,
            commit: await repoChatService.getCommit(),
            repoName: repoChatService.repoName,
            username: userInfo?.name ?? "",
            instantApplyMode: useInstantApplyMode,
            rules: parsedRules,
          });
          if (!prompt) {
            finishLoading();
            return { result: false };
          }
          /** codeSearch相关引用 */
          const ns = getRecordStoreByVendor("chat").getState().sessionHistory;
          if (ns) {
            setSessionHistory(
              produce(ns, (draft) => {
                if (!draft) {
                  return;
                }
                draft.cachedMessages[draft.cachedMessages.length - 1].A[0].codeSearchList = prompt?.list;
              }),
            );
          }
          formatQuery = prompt.prompt;
        }
      }

      const ns = getRecordStoreByVendor("chat").getState().sessionHistory;
      if (ns) {
        setSessionHistory(
          produce(ns, (draft) => {
            if (!draft) {
              return;
            }
            draft.cachedMessages[draft.cachedMessages.length - 1].Q.formatQuestion = formatQuery;
            draft.cachedMessages[draft.cachedMessages.length - 1].A[0].formatQuestion = formatQuery;
          }),
        );
      }

      /** 上报埋点 /指令 #知识库 */
      if (slashCommand && slashCommandStructure) {
        const param: ReportOpt<"shortcutInstruction"> = {
          key: "shortcutInstruction",
          type: getSlashCommandReportType(slashCommandStructure.command),
        };
        reportUserAction(param);
      }

      const contextLog = collectContextLog(originalEditorState, []);
      if (contextLog) {
        const param: ReportOpt<"input_context_send"> = {
          key: "input_context_send",
          type: contextLog,
          content: "common_chat",
        };
        reportUserAction(param);
      }

      postMessageUtil({
        rules: parsedRules,
        content: formatQuery,
        type: MessageType.SEND_MESSAGE,
        cachedMessages: sessionHistory?.cachedMessages ?? [],
        chatType: "intelligentChat",
        sessionId,
        uniqueId,
        useSearch,
        refFiles,
        chatModel: chatModel || { modelType: DEFAULT_MODEL_TYPE },
        docId,
        expiredIndex: sessionHistory ? [...sessionHistory.expiredIndex] : [],
        clearContextIndex: sessionHistory
          ? [...sessionHistory.clearContextIndex]
          : [],
        vendor: "chat",
        startTime,
      });
    }
    catch (e) {
      const message = e instanceof Error ? e.message : String(e);
      kwaiPilotBridgeAPI.showToast({
        level: "error",
        message: `提交过程错误(${message}), 请联系 kwaipilot oncall`,
      });
      finishLoading();
      throw e;
    }

    return {
      result: true,
    };

    // 发送给extension
  }, [userInfo, clearSuggestQuestion, activeSession, chatModel, setLoadingStatu, abortCurrentChat, sessionHistory, setSessionHistory, workspaceInfo?.result, navigate, setActiveSession, summaryConversation, updateHistoryList, finishLoading]);

  return {
    doSubmit,
  };
}

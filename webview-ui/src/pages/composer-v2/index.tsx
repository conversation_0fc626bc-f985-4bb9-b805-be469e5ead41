import { HistoryBar } from "@/components/HistoryBar";
import VideoBg from "@/components/VideoBg";
import { useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { NewComposerButton } from "./NewComposerButton";
import { withProviders } from "@udecode/cn";
import { ComposerSession } from "@/logics/composer/ComposerSession";
import {
  ComposerStateContextProvider,
  useComposerState,
} from "@/logics/composer/context/ComposerStateContext";
import { useBlocker, useSearchParams } from "react-router-dom";
import { IsNotCurrentWorkspace } from "./components/IsNotCurrentWorklspace";
import {
  LeaveConfirmDialog_DirtyWorkingSet,
  LeaveConfirmDialog_Streaming,
} from "./components/LeaveConfirmDialog";
import {
  getActiveSessionIdFromStorage,
  saveActiveSessionIdToStorage,
} from "./activeSessionStorage";
import { useUserStore } from "@/store/user";
import { NotLoggedHint } from "@/components/NotLoggedHint";
import {
  RestoreConfirmDialog,
  RestoreConfirmDialogContext,
} from "@/logics/composer/components/HumanMessage/RestoreConfirmDialog";
import {
  RestoreAndSendDialog,
  RestoreAndSendDialogContext,
} from "@/logics/composer/components/HumanMessage/RestoreAndSendDialog";
import {
  SendConfirmDialog,
  SendConfirmDialogContext,
} from "@/logics/composer/components/HumanMessage/SendConfirmDialog";
import { LocalServiceConnectionLostAlert } from "@/logics/composer/components/LocalServiceConnectionLostAlert";
import { LexicalEditor } from "lexical";
import Logo from "../home/<USER>";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import clsx from "clsx";
import { useAsync } from "react-use";
import { WorkspaceUnavailableHint } from "./components/WorkspaceUnavailableHint";
import { useSubmit } from "@/logics/composer/useSubmit";
import ApplyStatus from "@/logics/composer/components/ApplyStatus";
import { useViewChangeBlocker } from "@/hooks/useViewChangeBlocker";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { McpStatusBar } from "@/logics/composer/components/Mcp/McpStatusBar";
import { ModelSelector } from "@/logics/composer/components/ModelSelector";
import { ComposerUserInputTextarea } from "@/logics/composer/components/ComposerUserInputTextarea";
import { useVSCodeServices } from "@/hooks/useVSCodeServices";
import eventBus from "@/utils/eventBus";
import IDELogo from "@/assets/ide-logo.svg?react";
import { PortalContainerProvider } from "@/components/ui/Portal";

export function WelcomeContent() {
  const [, isKwaiPilotIDE] = useIdeEnv();

  if (isKwaiPilotIDE) return (
    <div className="w-full relative flex px-[16px] flex-col justify-start items-center overflow-auto pb-[56px]">
      <div className="w-full flex items-end justify-center relative flex flex-col justify-start items-center gap-y-3">
        <div className="logo text-tab-activeForeground"><IDELogo /></div>
        <div className="font-['PingFang_SC'] font-normal text-[16px] leading-[100%] tracking-[0.04em] text-center text-tab-inactiveForeground">Code Smarter, Build Faster</div>
      </div>
    </div>
  );

  return (
    <div className="w-full mt-[15vh] relative flex px-[16px] flex-col justify-start items-center overflow-auto pb-[48px]">
      <div className=" w-full  flex items-end justify-center relative">
        <Logo></Logo>
      </div>
    </div>
  );
}

export function WrapperIDEWelcome(props: { children?: React.ReactNode }) {
  return (
    <div className="flex-1 flex flex-col h-full align-center justify-center">
      {props.children}
    </div>
  );
};

const PageComposerBody = withProviders(
  RestoreAndSendDialogContext,
  RestoreConfirmDialogContext,
  SendConfirmDialogContext,
)(() => {
  const [
    isNotCurrentWorkspaceDialogShown,
    setIsNotCurrentWorkspaceDialogShown,
  ] = useState(false);

  const {
    isCurrentWorkspaceSession,
    setMessageScrollContainerHeight,
    localServiceConnectionLost,
    localMessages,
    isStreaming,
    sessionId,
    editingMessageTs,
  } = useComposerState();

  const isInWelcomeMode = localMessages.length === 0;

  const messageScrollContainer = useRef<HTMLDivElement>(null);

  const editorRef = useRef<LexicalEditor>(null);

  const openUserManual = useCallback(() => {
    kwaiPilotBridgeAPI.openUrl(
      "https://docs.corp.kuaishou.com/k/home/<USER>",
    );
  }, []);

  useEffect(() => {
    if (!messageScrollContainer.current) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setMessageScrollContainerHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(messageScrollContainer.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [
    messageScrollContainer,
    setMessageScrollContainerHeight,
    isInWelcomeMode,
  ]);

  const onReadonlyMaskClick = useCallback(() => {
    if (editingMessageTs) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [editingMessageTs]);

  const { doSubmit } = useSubmit({
    role: "bottom",
  });

  const userInfo = useUserStore(state => state.userInfo);
  // 是否展示[停止生成

  const [, isKwaiPilotIDE] = useIdeEnv();

  const handleCloseIsNotCurrentWorkspaceDialog = useCallback(() => {
    setIsNotCurrentWorkspaceDialogShown(false);
  }, []);

  useEffect(() => {
    if (!isCurrentWorkspaceSession && !isInWelcomeMode) {
      setIsNotCurrentWorkspaceDialogShown(true);
    }
    else {
      setIsNotCurrentWorkspaceDialogShown(false);
    }
  }, [
    handleCloseIsNotCurrentWorkspaceDialog,
    isCurrentWorkspaceSession,
    isInWelcomeMode,
  ]);

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  const moreOpt = (
    <>
      {isDeveloperMode || isKwaiPilotIDE ? <ModelSelector /> : null}
      <McpStatusBar />

    </>
  );

  const scrollOuterClickHandler = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.target === e.currentTarget) {
        kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
      }
    },
    [],
  );

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);

  /**
   * 用于挂载页面内的 Portal
   *
   * Portal 默认会放到 document.body 上, 但composer 页面本身是占满全屏的, 如果 Portal 元素溢出屏幕,会导致产生滚动
   * https://team.corp.kuaishou.com/task/********
   *
   * 当然可以通过设置 document.body 的 overflow: hidden 来解决, 但这样可能会影响到其他页面的滚动
   *
   * 因此拥有一个局部的容器来挂载 Portal 是更好的选择
   *  */
  const globalContainerRef = useRef<HTMLDivElement | null>(null);

  if (!userInfo) {
    return <NotLoggedHint p={4} />;
  }

  const renderMain = () => {
    return (
      <>
        {isInWelcomeMode
          ? (
              <WelcomeContent />
            )
          : (
              <div
                className="flex flex-col justify-between w-full h-full overflow-hidden relative"
                onClick={scrollOuterClickHandler}
              >
                <div
                  ref={messageScrollContainer}
                  className="overflow-y-scroll overscroll-none px-[24px] pt-3 flex text-text-main flex-col-reverse chat-dialog-container"
                  id="composer-v2-message-scroll-container"
                  style={{
                    scrollbarColor:
                "var(--vscode-scrollbarSlider-background) transparent",
                    scrollbarWidth: "thin",
                  }}
                >
                  <div className="w-full flex-auto">
                    <PortalContainerProvider value={{ containerRef: globalContainerRef }}>
                      <ComposerSession />
                    </PortalContainerProvider>
                  </div>
                </div>
                <div data-container-ref ref={globalContainerRef}></div>
              </div>
            )}
        <div
          className={clsx(
            "flex-none relative",
            isKwaiPilotIDE && isInWelcomeMode
              ? "flex flex-col justify-center"
              : "",
          )}
        >
          {isNotCurrentWorkspaceDialogShown && (
            <IsNotCurrentWorkspace
              onCancel={handleCloseIsNotCurrentWorkspaceDialog}
            >
            </IsNotCurrentWorkspace>
          )}

          <RestoreConfirmDialog />
          <RestoreAndSendDialog />
          <SendConfirmDialog />
          {localServiceConnectionLost && (
            <LocalServiceConnectionLostAlert px={6} mb={4} />
          )}
          {(isCurrentWorkspaceSession || isInWelcomeMode) && (
            <ComposerUserInputTextarea
              editorRef={editorRef}
              editorClassName={isInWelcomeMode ? "h-[80px]" : "min-h-[44px]"}
              wrapperClassName="p-3 pt-0"
              enable={
                isInWelcomeMode
                  ? false
                  : {
                      top: true,
                      right: false,
                      bottom: false,
                      left: false,
                      topRight: false,
                      bottomRight: false,
                      bottomLeft: false,
                      topLeft: false,
                    }
              }
              role="bottom"
              doSubmit={doSubmit}
              doStop={stopCurrentTask}
              isStreaming={isStreaming}
              sessionId={sessionId}
              isContextConsumer={/* 有正在编辑的历史消息, 则置后 */!editingMessageTs}
              onClick={onReadonlyMaskClick}
              applyStatusElement={<ApplyStatus />}
              moreOpt={moreOpt}
            />
          )}
          {isInWelcomeMode && !isKwaiPilotIDE && (
            <div className="mt-2 flex gap-[6px] text-[12px] justify-center leading-[18px] text-[#B4BCD0]">
              更多能力详见
              <div
                className="text-[#5AA7FF] cursor-pointer"
                onClick={openUserManual}
              >
                Kwaipilot使用手册
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  if (isKwaiPilotIDE && isInWelcomeMode) {
    return (
      <WrapperIDEWelcome>
        {renderMain()}
      </WrapperIDEWelcome>
    );
  }

  return renderMain();
});

const InnerPageComposer = () => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const { sessionId, isStreaming, indeterminatedWorkingSetEffects, localMessages }
    = useComposerState();

  const isInWelcomeMode = localMessages.length === 0;

  // 获取VSCode服务访问器
  const vsCodeServices = useVSCodeServices();

  const [searchParams] = useSearchParams();

  const [isLoading, setIsLoading] = useState(true);

  const userInfo = useUserStore(state => state.userInfo);

  useEffect(() => {
    const initSession = async () => {
      const sessionIdFromQuery = searchParams.get("sessionId");
      if (sessionIdFromQuery === sessionId) {
        setIsLoading(false);
        return;
      }
      // 由于getActiveSessionIdFromStorage是异步的，需要使用await
      let targetSessionId = sessionIdFromQuery;
      if (!targetSessionId) {
        targetSessionId = await getActiveSessionIdFromStorage();
      }
      if (targetSessionId) {
        saveActiveSessionIdToStorage(targetSessionId);
        setIsLoading(true);
        kwaiPilotBridgeAPI.extensionComposer
          .$showTaskWithId(targetSessionId)
          .finally(() => {
            setIsLoading(false);
          });
      }
      else {
        setIsLoading(false);
      }
    };

    initSession();
    // 这个 hook 作用是在 url 变化时更新 task，因此 sessionId 变化时 不应该执行
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    if (sessionId) {
      saveActiveSessionIdToStorage(sessionId);
    }
  }, [sessionId]);

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);

  const routeBlocker = useBlocker(
    () =>
      Boolean(userInfo)
      && (isStreaming || indeterminatedWorkingSetEffects.length > 0),
  );
  const [newComposerBlocked, setNewComposerBlocked] = useState(false);
  const onStreamingContinue = useCallback(() => {
    stopCurrentTask();
    if (routeBlocker.state === "blocked") {
      routeBlocker.proceed?.();
    }
    else if (newComposerBlocked) {
      setNewComposerBlocked(false);
      kwaiPilotBridgeAPI.clearTask();
      saveActiveSessionIdToStorage("");
    }
  }, [newComposerBlocked, routeBlocker, stopCurrentTask]);

  const showBlockDialog
    = routeBlocker.state === "blocked" || newComposerBlocked;
  const onDirtyWorkingSetContinue = useCallback(
    async (action: "accept" | "reject") => {
      if (action === "accept") {
        await kwaiPilotBridgeAPI.editor.keepDiff({ abortChat: true });
      }
      else {
        await kwaiPilotBridgeAPI.editor.undoDiff({ abortChat: true });
      }
      if (routeBlocker.state === "blocked") {
        routeBlocker.proceed?.();
      }
      else if (newComposerBlocked) {
        setNewComposerBlocked(false);
        kwaiPilotBridgeAPI.clearTask();
        saveActiveSessionIdToStorage("");
      }
    },
    [newComposerBlocked, routeBlocker],
  );

  const clearBlockState = useCallback(() => {
    routeBlocker.reset?.();
    setNewComposerBlocked(false);
  }, [routeBlocker]);

  const onNewComposerButtonClick = useCallback(() => {
    if (isStreaming || indeterminatedWorkingSetEffects.length > 0) {
      setNewComposerBlocked(true);
    }
    else {
      kwaiPilotBridgeAPI.clearTask();
      saveActiveSessionIdToStorage("");
    }
  }, [indeterminatedWorkingSetEffects.length, isStreaming]);

  // 修改当前聊天prompt的方法
  const modifyCurrentChatPrompt = useCallback(async (newPrompt: string) => {
    eventBus.emit("composer:setValue", { text: newPrompt });
    console.log("Current chat prompt modified successfully:", newPrompt);
    return { success: true, sessionId, newPrompt };
  }, [sessionId]);

  // 打开新聊天并设置prompt的方法
  const openNewChatWithPrompt = useCallback(async (prompt: string) => {
    try {
      // 先创建新聊天
      kwaiPilotBridgeAPI.clearTask();
      saveActiveSessionIdToStorage("");

      // 等待新聊天创建完成后设置prompt
      // 这里可能需要一个延迟或者监听新session创建的事件
      setTimeout(async () => {
        try {
          eventBus.emit("composer:setValue", { text: prompt });
          console.log("New chat opened with prompt:", prompt);
        }
        catch (error) {
          console.error("Failed to set initial prompt for new chat:", error);
        }
      }, 100);

      return { success: true, prompt };
    }
    catch (error) {
      console.error("Failed to open new chat with prompt:", error);
      throw error;
    }
  }, []);

  // 注册sidebar操作方法
  useEffect(() => {
    if (!vsCodeServices?.sideActionService) {
      return;
    }

    const sideActionService = vsCodeServices.sideActionService;

    // 注册修改当前聊天prompt的操作
    const modifyPromptAction = {
      id: "kwaipilot.composer.modifyCurrentPrompt",
      name: "修改当前聊天Prompt",
      description: "修改当前聊天会话的prompt",
      handler: modifyCurrentChatPrompt,
      context: null,
    };

    // 注册打开新聊天并设置prompt的操作
    const newChatWithPromptAction = {
      id: "kwaipilot.composer.openNewChatWithPrompt",
      name: "打开新聊天并设置Prompt",
      description: "创建新的聊天会话并设置初始prompt",
      handler: openNewChatWithPrompt,
      context: null,
    };

    // 尝试注册操作
    const modifyRegistered = sideActionService.registerAction(modifyPromptAction);
    const newChatRegistered = sideActionService.registerAction(newChatWithPromptAction);

    if (modifyRegistered) {
      console.log("[Composer] Registered modify current prompt action");
    }
    else {
      console.warn("[Composer] Failed to register modify current prompt action");
    }

    if (newChatRegistered) {
      console.log("[Composer] Registered new chat with prompt action");
    }
    else {
      console.warn("[Composer] Failed to register new chat with prompt action");
    }

    // 清理函数，组件卸载时注销操作和卸载UI
    return () => {
      sideActionService.unregisterAction("kwaipilot.composer.modifyCurrentPrompt");
      sideActionService.unregisterAction("kwaipilot.composer.openNewChatWithPrompt");
    };
  }, [vsCodeServices, modifyCurrentChatPrompt, openNewChatWithPrompt]);

  // 使用视图切换阻断Hook
  const viewChangeBlocker = useViewChangeBlocker({
    shouldBlock: () => Boolean(userInfo) && (isStreaming || indeterminatedWorkingSetEffects.length > 0),
    onBlockedContinue: stopCurrentTask,
    hasIndeterminatedWorkingSet: indeterminatedWorkingSetEffects.length > 0,
  });

  const { value: workspaceFile } = useAsync(
    () => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(),
    [],
  );

  const isInWorkspace = Boolean(workspaceFile);

  return (
    <div className="flex flex-col h-full min-w-[299px] overflow-hidden bg-sideBar-background">
      {!isDark && <VideoBg />}
      <HistoryBar
        current="composer-v2"
        {...viewChangeBlocker}
        action={
          userInfo && !isInWorkspace && !isInWelcomeMode
            ? (
                <NewComposerButton onClick={onNewComposerButtonClick} />
              )
            : null
        }
      />
      {isLoading
        ? null
        : isInWorkspace
          ? (
              <WorkspaceUnavailableHint />
            )
          : (
              <PageComposerBody />
            )}
      {showBlockDialog
      && (indeterminatedWorkingSetEffects.length > 0
        ? (
            <LeaveConfirmDialog_DirtyWorkingSet
              indeterminatedWorkingSetFileNum={
                indeterminatedWorkingSetEffects.length
              }
              isOpen={showBlockDialog}
              onCancel={clearBlockState}
              onContinue={onDirtyWorkingSetContinue}
              onClose={clearBlockState}
            />
          )
        : (
            <LeaveConfirmDialog_Streaming
              isOpen={showBlockDialog}
              onCancel={clearBlockState}
              onContinue={onStreamingContinue}
              onClose={clearBlockState}
            />
          ))}
    </div>
  );
};

export const PageComposerV2 = withProviders(ComposerStateContextProvider)(
  InnerPageComposer,
);

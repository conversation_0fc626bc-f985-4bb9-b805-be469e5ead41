import { kwaiPilotBridgeAPI } from "@/bridge";
import { SingleIcon } from "@/components/SingleIcon";
import { Icon } from "@/components/Union/t-iconify";
import { useHistoryStore } from "@/store/history";
import { getRecordStoreByVendor } from "@/store/record";

const ClearHistory = () => {
  const setHistoryList = useHistoryStore(state => state.setHistoryList);
  const chatVendorRecordStore = getRecordStoreByVendor("chat");
  const composerVendorRecordStore = getRecordStoreByVendor("composer");

  const clearHistory = () => {
    setHistoryList([]);
    chatVendorRecordStore.getState().setActiveSession({ value: "" });
    composerVendorRecordStore.getState().setActiveSession({ value: "" });
    kwaiPilotBridgeAPI.clearSession();
  };
  return (
    <SingleIcon
      onClick={clearHistory}
      className="size-[20px]"
      title="清空历史会话"
    >
      <Icon icon="codicon:trash" />
    </SingleIcon>
  );
};

export default ClearHistory;

import { Dialog } from "@/components/Dialog";
import {
  RecordProvider,
  useRecordStore,
} from "@/store/record";
import { collectClick } from "@/utils/weblogger";
import { useCallback, useRef, useState } from "react";
import StopIcon from "@/assets/stop.svg?react";
import clsx from "clsx";
import { useColorMode } from "@chakra-ui/react";
import VideoBg from "@/components/VideoBg";
import { HistoryBar } from "@/components/HistoryBar";
import DialogMask from "./components/DialogMask";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { NewDialog } from "@/components/TextArea/components/NewDialog";
import { withProps, withProviders } from "@udecode/cn";
import { useUserStore } from "@/store/user";
import { NotLoggedHint } from "@/components/NotLoggedHint";
import { useChatSubmit } from "@/logics/chat/useChatSubmit";
import { ChatUserInputTextarea } from "@/logics/chat/ChatUserInputTextarea";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import { useViewChangeBlocker } from "@/hooks/useViewChangeBlocker";
import { useScrollToBottom } from "@/pages/chat/components/useScrollToBottom";
import { UserInputTextareaProps } from "@/logics/UserInputTextarea/UserInputTextArea";
import { WelcomeContent, WrapperIDEWelcome } from "../composer-v2";

function InnerChatBody() {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const finishLoading = useRecordStore(state => state.finishLoading);
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const sessionHistory = useRecordStore(state => state.sessionHistory);
  const activeSession = useRecordStore(state => state.activeSession);
  const abortCurrentChat = useRecordStore(state => state.abortCurrentChat);
  const signalMessageDone = useRecordStore(state => state.signalMessageDone);
  const setStopReceiveMessageId = useRecordStore(
    state => state.setStopReceiveMessageId,
  );

  const [, setShowMask] = useState(false);

  const onStop = () => {
    abortCurrentChat();
    // 置为 loading false
    if (sessionHistory) {
      const currentQA = sessionHistory.cachedMessages.find(
        v => v.id === loadingStatu?.id,
      );
      currentQA
      && kwaiPilotBridgeAPI.addMessage({
        item: currentQA,
        sessionId: sessionHistory.sessionId,
        chatId: loadingStatu?.id ?? "",
      });
    }
    setStopReceiveMessageId(loadingStatu?.id ?? "");
    signalMessageDone(loadingStatu?.id ?? "", {
      finishReason: "aborted",
    });
    finishLoading();
    collectClick("VS_STOP_BUTTON");
  };
  const renderStopButton = () => (
    <div
      onClick={onStop}
      className="absolute left-[calc(50%-42px)] -top-[44px] h-[34px] p-[8px] rounded-[4px] bg-list-inactiveSelectionBackground flex items-center gap-[4px] hover:bg-list-dropBackground cursor-pointer shadow-stop-button z-100"
    >
      <div className={clsx(isDark ? "#E5ECF2" : "#77808B")}>
        <StopIcon />
      </div>
      <div className="text-[13px] font-normal leading-[18px] text-foreground">
        停止生成
      </div>
    </div>
  );
  const chatDialogContainerRef = useRef<HTMLDivElement>(null);
  const chatDialogContentRef = useRef<HTMLDivElement>(null);

  const scrollHandler = useScrollToBottom(chatDialogContainerRef, chatDialogContentRef);

  const { doSubmit: _doSubmit } = useChatSubmit();

  const doSubmit = useCallback<UserInputTextareaProps["doSubmit"]>(async (state) => {
    const result = await _doSubmit(state);
    if (result.result) {
      // 立即滚动到底部
      setTimeout(() => {
        scrollHandler.scrollDomToBottom();
      }, 100);
    }
    return result;
  }, [_doSubmit, scrollHandler]);

  const isInWelcomeMode = !sessionHistory?.cachedMessages.length;
  const [, isKwaiPilotIDE] = useIdeEnv();

  const renderMain = () => {
    return (
      <>
        {isInWelcomeMode
          ? <WelcomeContent />
          : (
              <div className="flex flex-col justify-between w-full h-full overflow-hidden">
                <div
                  ref={chatDialogContainerRef}
                  className="overflow-y-scroll overscroll-none px-[12px] pt-3 chat-dialog-container"
                  style={{
                    scrollbarColor: "var(--vscode-scrollbarSlider-background) transparent",
                    scrollbarWidth: "thin",
                  }}
                >
                  <div className="w-full" ref={chatDialogContentRef}>
                    <Dialog />
                    <DialogMask setShowMask={setShowMask} />
                  </div>
                </div>
              </div>
            )}
        <div className="flex-none relative">
          {/* <div
          className={clsx(
            "h-6 w-full absolute -top-[18px] left-3 -right-3 pointer-events-none transition-opacity",
            showMask ? "opacity-100" : "opacity-0",
          )}
          style={{
            background:
                  "linear-gradient(0deg, var(--vscode-sideBar-background) 0%, rgba(9, 20, 32, 0.00) 100%)",
            backdropFilter: " blur(0.25px)",
          }}
        >
        </div> */}
          {loadingStatu
          && loadingStatu.status === "loading"
          && !isInWelcomeMode
          && renderStopButton()}
          <ChatUserInputTextarea
            role="bottom"
            isStreaming={Boolean(loadingStatu && loadingStatu.status === "loading")}
            doSubmit={doSubmit}
            doStop={onStop}
            isContextConsumer={true}
            sessionId={activeSession}
            editorClassName={isInWelcomeMode ? "h-[80px]" : "min-h-[44px]"}
            wrapperClassName={"max-h-[400px] " + isKwaiPilotIDE ? "px-3 pb-3 pt-0" : "p-3"}
            enable={
              isInWelcomeMode
                ? false
                : {
                    top: true,
                    right: false,
                    bottom: false,
                    left: false,
                    topRight: false,
                    bottomRight: false,
                    bottomLeft: false,
                    topLeft: false,
                  }
            }
          >
          </ChatUserInputTextarea>
        </div>
      </>
    );
  };

  if (isKwaiPilotIDE && isInWelcomeMode) {
    return (
      <WrapperIDEWelcome>
        {renderMain()}
      </WrapperIDEWelcome>
    );
  }

  return renderMain();
}

const InnerChat = () => {
  const userInfo = useUserStore(state => state.userInfo);
  const sessionHistory = useRecordStore(state => state.sessionHistory);
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const abortCurrentChat = useRecordStore(state => state.abortCurrentChat);

  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";

  const isInWelcomeMode = !sessionHistory?.cachedMessages.length;

  // 使用视图切换阻断Hook
  const viewChangeBlocker = useViewChangeBlocker({
    shouldBlock: () => Boolean(userInfo) && Boolean(loadingStatu && loadingStatu.status === "loading"),
    onBlockedContinue: abortCurrentChat,
    hasIndeterminatedWorkingSet: false,
  });

  return (
    <div className="flex flex-col h-full min-w-[299px] overflow-none bg-sideBar-background">
      {!isDark && <VideoBg />}
      <HistoryBar
        action={isInWelcomeMode ? null : <NewDialog />}
        {...viewChangeBlocker}
      />
      {!userInfo
        ? (
            <NotLoggedHint p={4} />
          )
        : sessionHistory && sessionHistory.isComposer
          ? (
              <div className="text-text-common-secondary px-6 py-6 flex items-center gap-2">
                <div className="mt-[1px]">
                  当前处于助理模式(beta版), 如需AI对话 请创建
                </div>
                <NewDialog />
              </div>
            )
          : <InnerChatBody />}
    </div>
  );
};

export const Chat = withProviders(
  withProps(RecordProvider, {
    vendor: "chat",
  }),
)(InnerChat);

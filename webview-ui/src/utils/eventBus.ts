import { RecordStoreVendor } from "@/store/record";
import { EventEmitter } from "@infra-node/tee";
import { CodeActionParams, DisableRichEditorMenu } from "@shared/types";
import { useEffect } from "react";
import { InternalLocalMessage_Human } from "shared/lib/agent";
import { SerializedEditorState } from "lexical";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";

export type EventBusEvents = {
  "inlineChat": () => void;
  "actionResult": ({
    data,
    id,
  }: {
    data: string;
    id: string;
    vendor: RecordStoreVendor;
  }) => void;
  "startInlineChat": ({
    chatId,
    question,
    sessionId,
  }: {
    chatId: string;
    question: string;
    sessionId: string;
  }) => void;
  "actionForCode": (params: CodeActionParams) => void;
  // repo chat
  "pushRichEditorPanelDisableMenu": (data: DisableRichEditorMenu) => void;
  "inlineChat:message": (data: string) => void;
  "inlineChat:messageDone": (data?: string) => void;
  "composer:checkPointConfirm": (editorState?: string) => void;
  "composer:onResetCheckpoint": (payload: {
    humanMessage: InternalLocalMessage_Human;
  }) => void;
  /**
   * 设置编辑器内容的事件
   * @param payload.text 纯文本内容 - 如果提供此参数，将使用此文本创建新的编辑器状态
   * @param payload.editorState 序列化的编辑器状态 - 如果提供此参数，将直接使用此状态
   * @param payload.contextItems 上下文项 - 可选，用于设置编辑器的上下文头
   */
  "composer:setValue": (payload: {
    text?: string;
    editorState?: SerializedEditorState;
    contextItems?: MentionNodeV2Structure[];
  }) => void;
};
const eventBus = new EventEmitter<EventBusEvents>();
export default eventBus;

export function useEventBusListener<T extends keyof EventBusEvents>(eventName: T, handler?: null | undefined | EventBusEvents[T]) {
  useEffect(() => {
    if (!handler) {
      return;
    }
    eventBus.on(eventName, handler);
    return () => {
      eventBus.off(eventName, handler);
    };
  }, [eventName, handler]);
}

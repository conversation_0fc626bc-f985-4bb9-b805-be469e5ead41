// theme.ts

// 1. import `extendTheme` function
import { vsCss } from "@/style/vscode";
import { menuAnatomy } from "@chakra-ui/anatomy";
import {
  extendTheme,
  createMultiStyleConfigHelpers,
  type ThemeConfig,
  defineStyleConfig,
  defineStyle,
} from "@chakra-ui/react";
import { getCurrentColorModeByThemeType, getCurrentThemeType } from "./ide";

// 2. Add your color mode config
const config: ThemeConfig = {
  initialColorMode: getCurrentColorModeByThemeType(getCurrentThemeType()),
  useSystemColorMode: false,
};
const { definePartsStyle, defineMultiStyleConfig }
  = createMultiStyleConfigHelpers(menuAnatomy.keys);

// define the base component styles
const menuBaseStyle = definePartsStyle({
  // define the part you're going to style
  button: {
    // this will style the MenuButton component
  },
  list: {
    // this will style the MenuList component
    padding: 0,
    overflow: "hidden",
    borderRadius: "6px",
    border: "1px solid #CFD3DA",
    boxShadow:
      "0px 1px 3px 0px rgba(31, 35, 40, 0.12), 0px 8px 24px 0px rgba(66, 74, 83, 0.12)",

    _dark: {
      background: "#1B232D",
      border: "1px solid #40474E",
    },
    _light: {
      background: "#ffffff",
    },
  },
  item: {
    // this will style the MenuItem and MenuItemOption components
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row-reverse",
    padding: "12px",
    fontSize: "12px",
    lineHeight: "18px",
    _dark: {
      borderTop: "1px solid #30363D",
      color: "#F7F8F8",
      background: "#1B232D",
      _first: {
        borderTop: "none",
      },
      _hover: {
        background: "#2D353E",
      },
      _checked: {
        background: "#3F464F",
      },
      _disabled: {
        color: "#50575E",
      },
    },
    _light: {
      borderTop: "1px solid #CFD3DA",
      color: "#262A2F",
      background: "#ffffff",
      _first: {
        borderTop: "none",
      },
      _hover: {
        background: "#F6F8FA",
      },
      _checked: {
        background: "#EAEEF2",
      },
      _disabled: {
        color: "#BBBDBF",
      },
    },
  },
  groupTitle: {
    // this will style the text defined by the title prop
    // in the MenuGroup and MenuOptionGroup components
  },
  command: {
    // this will style the text defined by the command
    // prop in the MenuItem and MenuItemOption components
  },
  divider: {
    // this will style the MenuDivider component
  },
});
const tooltipBaseStyle = {
  fontSize: "12px",
};

// 3. extend the theme
const theme = extendTheme({
  config,
  styles: {
    global: {
      body: {
        color: "none",
        padding: 0,
      },
    },
  },
  components: {
    Menu: defineMultiStyleConfig({
      baseStyle: menuBaseStyle,
    }),
    Tooltip: defineStyleConfig({
      baseStyle: tooltipBaseStyle,
    }),
  },
});

export default theme;

// 从 setting-ui/src/theme/button.ts 复制, 作为全局 style 对其他组件有影响
export const ButtonStyle = defineStyleConfig({
  // The styles all button have in common
  baseStyle: {
    fontWeight: "normal",
    borderRadius: "4px", // <-- border radius is same for all variants and sizes
    _disabled: {
      opacity: 0.4,
    },
  },
  // Two sizes: sm and md
  sizes: {
    xs: {
      fontSize: "13px",
      lineHeight: "18px",
      height: "32px",
      paddingInline: "0",
      padding: "0 12px",
    },
  },
  // Two variants: outline and solid
  variants: {
    outline: {
      border: "1px solid",
      borderColor: "var(--vscode-dropdown-border)",
      color: "var(--vscode-badge-foreground)",
      bg: "transparent",
      _hover: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _focus: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _expanded: {
        bg: "transparent",
        borderColor: "var(--vscode-focusBorder)",
      },
      _disabled: {
        borderColor: "var(--vscode-dropdown-border) !important",
      },
    },
    blueSolid: defineStyle({
      bg: "var(--vscode-button-background)",
      color: "var(--vscode-button-foreground)",
      border: "1px solid",
      borderColor: "transparent",
      _hover: {
        bg: vsCss.buttonHoverBackground,
        borderColor: "var(--vscode-focusBorder)",
      },
      _focus: {
        borderColor: "var(--vscode-focusBorder)",
      },
      _disabled: {
        _hover: {
          bg: "var(--vscode-button-background) !important",
        },
      },
    }),
    secondary: defineStyle({
      bg: vsCss.buttonSecondaryBackground,
      _hover: {
        bg: vsCss.buttonSecondaryHoverBackground,
      },
    }),
  },
  // The default size and variant values
  defaultProps: {
    size: "xs",
    variant: "outline",
  },
});

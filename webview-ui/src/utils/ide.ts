/**
 * 获取当前是否在 ide 环境中
 * @returns
 */
export function getCurrentEnvIsInIDE() {
  // This is set by the IDE when running the extension
  return window.__KWAIPILOT_ENV__IN_IDE__ === true;
}

/**
 * 获取当前的 themeType
 * @returns  dark | light | hcDark | hcLight
 */
export function getCurrentThemeType() {
  // This is set by the IDE when running the extension
  return window.__KWAIPILOT_ENV__THEME_TYPE__ ?? "dark";
}

/**
 * 根据 themeType 获取当前的 colorMode
 * @param themeType
 * @returns
 */
export function getCurrentColorModeByThemeType(themeType: "dark" | "light" | "hcDark" | "hcLight") {
  if (themeType === "dark" || themeType === "hcDark") {
    return "dark";
  }
  return "light";
}

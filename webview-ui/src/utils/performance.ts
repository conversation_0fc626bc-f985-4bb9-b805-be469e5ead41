/**
 * 性能监控工具
 * 用于监控组件渲染和状态更新的性能
 * 创建一个性能监控工具来帮助验证优化效果
 */
interface PerformanceEntry {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private entries = new Map<string, PerformanceEntry>();
  private isEnabled = process.env.NODE_ENV === "development";

  start(name: string) {
    if (!this.isEnabled) return;

    this.entries.set(name, {
      name,
      startTime: performance.now(),
    });
  }

  end(name: string) {
    if (!this.isEnabled) return;
    const entry = this.entries.get(name);
    if (entry) {
      entry.endTime = performance.now();
      entry.duration = entry.endTime - entry.startTime;

      console.log(`[Performance] ${name}: ${entry.duration.toFixed(2)}ms`);

      this.entries.delete(name);
    }
  }

  measure(name: string, fn: () => void) {
    if (!this.isEnabled) {
      fn();
      return;
    }

    this.start(name);
    fn();
    this.end(name);
  }

  async measureAsync(name: string, fn: () => Promise<void>) {
    if (!this.isEnabled) {
      return await fn();
    }

    this.start(name);
    await fn();
    this.end(name);
  }
}

export const performanceMonitor = new PerformanceMonitor();

/**
 * React Hook 用于监控组件渲染性能
 */
export function usePerformanceMonitor(componentName: string) {
  const start = () => performanceMonitor.start(`${componentName}_render`);
  const end = () => performanceMonitor.end(`${componentName}_render`);

  return { start, end };
}

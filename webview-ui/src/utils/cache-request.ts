/**
 * 缓存项接口
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

/**
 * 缓存请求配置
 */
interface CacheRequestOptions {
  /** 缓存键名 */
  key: string;
  /** 缓存过期时间（毫秒），默认 5 分钟 */
  ttl?: number;
  /** 是否强制刷新缓存，默认 false */
  forceRefresh?: boolean;
  /** 是否在后台刷新过期缓存，默认 true */
  backgroundRefresh?: boolean;
  /** 错误重试次数，默认 0 */
  retryCount?: number;
  /** 重试间隔（毫秒），默认 1000 */
  retryDelay?: number;
}

/**
 * 缓存存储
 */
class CacheStorage {
  private cache = new Map<string, CacheItem<any>>();
  private pendingRequests = new Map<string, Promise<any>>();

  /**
   * 获取缓存项
   */
  get<T>(key: string): CacheItem<T> | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() > item.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return item;
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, ttl: number): void {
    const now = Date.now();
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      expiresAt: now + ttl,
    };
    this.cache.set(key, item);
  }

  /**
   * 删除缓存项
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 检查是否有正在进行的请求
   */
  hasPendingRequest(key: string): boolean {
    return this.pendingRequests.has(key);
  }

  /**
   * 获取正在进行的请求
   */
  getPendingRequest<T>(key: string): Promise<T> | null {
    return this.pendingRequests.get(key) || null;
  }

  /**
   * 设置正在进行的请求
   */
  setPendingRequest<T>(key: string, promise: Promise<T>): void {
    this.pendingRequests.set(key, promise);

    // 请求完成后清除
    promise.finally(() => {
      this.pendingRequests.delete(key);
    });
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiresAt) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
const globalCache = new CacheStorage();

/**
 * 延迟函数
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带重试的请求执行
 */
async function executeWithRetry<T>(
  requestFn: () => Promise<T>,
  retryCount: number = 0,
  retryDelay: number = 1000,
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= retryCount; i++) {
    try {
      return await requestFn();
    }
    catch (error) {
      lastError = error as Error;

      // 如果不是最后一次重试，等待后继续
      if (i < retryCount) {
        await delay(retryDelay);
        console.warn(`请求失败，第 ${i + 1} 次重试...`, error);
      }
    }
  }

  throw lastError!;
}

/**
 * 通用缓存请求方法
 *
 * @param requestFn 请求函数
 * @param options 缓存配置
 * @returns 返回缓存或请求的数据
 *
 * @example
 * ```typescript
 * // 基本使用
 * const userData = await cacheRequest(
 *   () => httpClient.getUserInfo(),
 *   { key: 'user-info' }
 * );
 *
 * // 带过期时间和强制刷新
 * const modelList = await cacheRequest(
 *   () => httpClient.getModelList(),
 *   {
 *     key: 'model-list',
 *     ttl: 10 * 60 * 1000, // 10分钟
 *     forceRefresh: false
 *   }
 * );
 *
 * // 带重试机制
 * const criticalData = await cacheRequest(
 *   () => httpClient.getCriticalData(),
 *   {
 *     key: 'critical-data',
 *     retryCount: 3,
 *     retryDelay: 2000
 *   }
 * );
 * ```
 */
export async function cacheRequest<T>(
  requestFn: () => Promise<T>,
  options: CacheRequestOptions,
): Promise<T> {
  const {
    key,
    ttl = 5 * 60 * 1000, // 默认5分钟
    forceRefresh = false,
    backgroundRefresh = true,
    retryCount = 0,
    retryDelay = 1000,
  } = options;

  // 如果不强制刷新，先检查缓存
  if (!forceRefresh) {
    const cachedItem = globalCache.get<T>(key);
    if (cachedItem) {
      // 如果启用后台刷新且缓存即将过期（剩余时间小于TTL的20%），在后台刷新
      if (backgroundRefresh) {
        const remainingTime = cachedItem.expiresAt - Date.now();
        const refreshThreshold = ttl * 0.2;

        if (remainingTime < refreshThreshold && !globalCache.hasPendingRequest(key)) {
          // 后台刷新，不等待结果
          const backgroundPromise = executeWithRetry(requestFn, retryCount, retryDelay)
            .then((data) => {
              globalCache.set(key, data, ttl);
              return data;
            })
            .catch((error) => {
              console.error(`后台刷新缓存失败 [${key}]:`, error);
            });

          globalCache.setPendingRequest(key, backgroundPromise);
        }
      }

      return cachedItem.data;
    }
  }

  // 检查是否有正在进行的相同请求
  const pendingRequest = globalCache.getPendingRequest<T>(key);
  if (pendingRequest) {
    return pendingRequest;
  }

  // 创建新的请求
  const requestPromise = executeWithRetry(requestFn, retryCount, retryDelay)
    .then((data) => {
      // 缓存结果
      globalCache.set(key, data, ttl);
      return data;
    })
    .catch((error) => {
      console.error(`缓存请求失败 [${key}]:`, error);
      throw error;
    });

  // 记录正在进行的请求
  globalCache.setPendingRequest(key, requestPromise);

  return requestPromise;
}

/**
 * 清除指定缓存
 */
export function clearCache(key: string): void {
  globalCache.delete(key);
}

/**
 * 清除所有缓存
 */
export function clearAllCache(): void {
  globalCache.clear();
}

/**
 * 获取缓存信息
 */
export function getCacheInfo(): {
  keys: string[];
  size: number;
} {
  return {
    keys: globalCache.keys(),
    size: globalCache.size(),
  };
}

/**
 * 清理过期缓存
 */
export function cleanupExpiredCache(): void {
  globalCache.cleanup();
}

/**
 * 创建带默认配置的缓存请求函数
 */
export function createCacheRequest(defaultOptions: Partial<CacheRequestOptions>) {
  return function<T>(
    requestFn: () => Promise<T>,
    options?: Partial<CacheRequestOptions>,
  ): Promise<T> {
    return cacheRequest(requestFn, {
      ...defaultOptions,
      ...options,
    } as CacheRequestOptions);
  };
}

// 定期清理过期缓存（每5分钟）
if (typeof window !== "undefined") {
  setInterval(() => {
    cleanupExpiredCache();
  }, 5 * 60 * 1000);
}

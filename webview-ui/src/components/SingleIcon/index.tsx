import { Tooltip } from "@/components/Union/chakra-ui";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";

export const SingleIcon = ({
  children,
  onClick,
  title,
  disabled,
  active,
  className,
}: {
  children: React.ReactNode;
  title?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  disabled?: boolean;
  active?: boolean;
  className?: string;
}) => {
  return (
    <Tooltip label={title}>
      <div
        onClick={disabled ? undefined : onClick}
        className={twMerge(clsx(
          "cursor-pointer hover:bg-toolbar-hoverBackground w-[24px] h-[24px] rounded-[4px] flex items-center justify-center",
          {
            "bg-inputOption-activeBackground": active,
          },
          className,
        ), className)}
        style={{
          opacity: disabled ? 0.5 : 1,
          cursor: disabled ? "not-allowed" : "pointer",
        }}
      >
        {children}
      </div>
    </Tooltip>
  );
};

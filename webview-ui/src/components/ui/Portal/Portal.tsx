import { Portal as ChakraPortal } from "@/components/Union/chakra-ui";
import { PortalProps } from "@chakra-ui/react";
import { createContext } from "@/utils/context";

export interface PortalContainerContext {
  containerRef: React.RefObject<HTMLElement>;
}

const [PortalMountProvider, usePortalContainerContext] = createContext<PortalContainerContext | null>({
  name: "PortalContext",
  strict: false,
});

export { PortalMountProvider as PortalContainerProvider };

/**
 * 基于 Chakra UI 的 Portal 组件, 新增了<PortalContainerProvider> 可以集中管理 Portal 默认的 container
 *
 * ```jsx
 * <PortalContainerProvider containerRef={containerRef}>
 *  <Portal>
 *   <div>内容</div>
 *  </Portal>
 * </PortalContainerProvider>
 * ```
 * @param props
 * @returns
 */
export function Portal(props: PortalProps) {
  const { containerRef: containerRefProp, ...rest } = props;
  const mountContext = usePortalContainerContext();

  return <ChakraPortal containerRef={containerRefProp || mountContext?.containerRef} {...rest}></ChakraPortal>;
}

import React, { useCallback } from "react";
import { useComposerState } from "@/logics/composer/context/ComposerStateContext";
import { Switch, Box, Text, useColorMode } from "@chakra-ui/react";
import { Tooltip } from "@/components/Union/chakra-ui";

export interface FocusModeToggleProps {
  className?: string;
}

/**
 * 专注模式切换组件
 * 开启时会新开一个 tab，关闭时也会新开一个 tab
 */
export const FocusModeToggle: React.FC<FocusModeToggleProps> = ({ className }) => {
  const { focusMode, toggleFocusMode, localMessages } = useComposerState();
  const { colorMode } = useColorMode();

  const isInWelcomeMode = localMessages.length === 0;
  const handleToggle = useCallback((checked: boolean) => {
    toggleFocusMode(checked);
  }, [toggleFocusMode]);

  if (!isInWelcomeMode) {
    return focusMode ? "深度模式" : "普通模式";
  }

  return (
    <Tooltip
      label={focusMode ? "关闭专注模式" : "开启专注模式"}
      placement="top"
    >
      <Box
        className={className}
        display="flex"
        alignItems="center"
        gap={2}
        cursor="pointer"
        onClick={() => handleToggle(!focusMode)}
        borderRadius="md"
        _hover={{
          bg: colorMode === "dark" ? "gray.700" : "gray.100",
        }}
      >
        <Text
          className=" cursor-pointer leading-[18px] inline-block text-foreground text-[13px] py-[3px]"
        >
          深度模式
        </Text>

        <Switch
          isChecked={focusMode}
          onChange={(e) => {
            e.stopPropagation();
            handleToggle(e.target.checked);
          }}
          size="sm"
          colorScheme="blue"
        />
      </Box>
    </Tooltip>
  );
};

export default FocusModeToggle;

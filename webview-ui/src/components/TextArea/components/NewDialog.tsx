import { useRecordStore } from "@/store/record";
import { collectClick } from "@/utils/weblogger";
import { Tooltip } from "@/components/Union/chakra-ui";
import { DEFAULT_MODEL_TYPE } from "@/constant";
import { SingleIcon } from "@/components/SingleIcon";
import { Icon } from "@/components/Union/t-iconify";

export const NewDialog: React.FC = () => {
  const setActiveSession = useRecordStore(state => state.setActiveSession);
  const setChatModelType = useRecordStore(state => state.setChatModelType);

  const createNewDialog = () => {
    collectClick("VS_CREATE_OR_CHANGE_CHAT");
    setActiveSession({ value: "" });
    setChatModelType(DEFAULT_MODEL_TYPE);
  };

  return (
    <Tooltip>
      <SingleIcon
        onClick={createNewDialog}
        className="size-[20px]"
        title="新会话"
      >
        <Icon icon="mynaui:chat-plus" />
      </SingleIcon>
    </Tooltip>
  );
};

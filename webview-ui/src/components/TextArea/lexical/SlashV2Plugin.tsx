import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { forwardRef, useCallback, useEffect, useState } from "react";
import { useRecentUsedPrompts } from "../components/NewPanel/useRecentUsedPrompts";
import repoChatService from "@/services/repo-chat";
import { MentionTypeaheadOption, TypeaheadMenuOptionType, useSlashOptions } from "@/logics/UserInputTextarea/MentionPanel/useOptions";
import { useAsync, useLatest } from "react-use";
import { throwNeverError } from "@/utils/throwUnknownError";
import { $createTextNode, $getRoot, $insertNodes, $isElementNode, COMMAND_PRIORITY_NORMAL, KEY_ESCAPE_COMMAND, LexicalNode, TextNode } from "lexical";
import { weblog } from "@/utils/weblogger";
import { CustomPromptData } from "shared/lib/CustomVariable";
import { firstValueFrom } from "rxjs";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { $compileLexicalNodes } from "../CustomVariable/renderLexicalNodes";
import { TypeaheadMenuContext } from "./MentionsV2PluginContext";
import { MentionPanel } from "@/logics/UserInputTextarea/MentionPanel";
import { MenuRenderFn, MenuResolution } from "@lexical/react/LexicalTypeaheadMenuPlugin";
import { URI } from "vscode-uri";
import { useRichEditorContext } from "../hooks/useRichEditorContext";
import { createMatcher, MenuRenderPortal } from "./MentionsV2Plugin";
import { useUserInputTextAreaContext } from "@/logics/UserInputTextarea/UserInputTextAreaContext";
import { DOM } from "@/utils/dom";
import { BetterLexicalTypeaheadMenuPlugin } from "./BetterLexicalTypeaheadMenuPlugin";
import { httpClient } from "@/http";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { $createMentionNodeV2, $isMentionNodeV2 } from "./MentionNodeV2/MentionNodeV2";
import { CommandPrefix, DisableRichEditorMenu } from "@shared/types";
import { usePromptTemplate } from "@/store/promptTemplate";

/**
 * 可以被当做 currentMenu 的类型
 */
const ALL_MENU_TYPES = [TypeaheadMenuOptionType.customPrompt];

const mentionPanelMatcher = createMatcher(["/"]);

export const SlashV2Plugin = forwardRef<JSX.Element | null, { mode: "chat" | "composer" }>(({ mode }, _ref) => {
  const [editor] = useLexicalComposerContext();

  const [queryString, setQueryString] = useState<string | null>(null);

  const { reportCustomPromptRecentUsed } = useRecentUsedPrompts();

  const setPromptTemplate = usePromptTemplate(s => s.setPromptTemplate);

  const repoPath = useAsync(() => repoChatService.getWorkspacePathAndRepoPath().then(res => res.repoPath), []);

  const { setSlashV2Shown, slashV2Shown } = useRichEditorContext();

  const setCustomPrompts = useRichEditPanelMenuStore(s => s.setCustomPrompts);
  const setDisabledMenu = useRichEditPanelMenuStore(s => s.setDisabledMenu);

  const [currentMenu, setCurrentMenu] = useState<TypeaheadMenuOptionType>(TypeaheadMenuOptionType.none);

  const onSelectOption = useCallback(
    (
      menu: MentionTypeaheadOption,
      nodeToReplace: TextNode | null,
      closeMenu: () => void,
    ) => {
      if (menu.type === TypeaheadMenuOptionType.heading) {
        const targetMenu = ALL_MENU_TYPES.find(type => type === menu.name);
        if (targetMenu) {
          setCurrentMenu(targetMenu);
        }
      }
      else if (menu.type === TypeaheadMenuOptionType.customPrompt) {
        const raw = menu.structure as CustomPromptData;
        return (async () => {
          reportCustomPromptRecentUsed(raw);
          weblog?.sendImmediately("CLICK", {
            action: "VS_CUSTOM_PROMPT_ITEM",
            params: {
              id: raw.id,
              name: raw.name,
            },
            type: "USER_OPERATION",
          });
          const currentFile = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection());
          const language = (await kwaiPilotBridgeAPI.getActiveEditor()).document.language;
          let updateFailed = false;
          editor.update(() => {
            let nodes: LexicalNode[] = [];
            try {
              nodes = $compileLexicalNodes(raw.content, {
                repoPath: repoPath.value,
                repoName: repoChatService.repoName,
                currentFile: currentFile
                  ? {
                      type: "file",
                      ...currentFile,
                      uri: URI.parse(currentFile.uri),
                    }
                  : null,
                language,
              });
            }
            catch (e) {
              kwaiPilotBridgeAPI.showToast({
                message: `解析 prompt 模板失败，请尝试联系prompt负责人，错误信息:${e instanceof Error ? e.message : String(e) || "unknown"}`,
                level: "error",
              });
              updateFailed = true;
              return;
            }
            const root = $getRoot();
            root.clear();
            $insertNodes(nodes);
          });
          if (updateFailed) {
            closeMenu();
            return;
          }
        })();
      }
      else if (menu.type === TypeaheadMenuOptionType.file) {
        // 不会有 file 类型
      }
      else if (menu.type === TypeaheadMenuOptionType.folder) {
        // 不会有 folder 类型
      }
      else if (menu.type === TypeaheadMenuOptionType.rule) {
        // 不会有 rule 类型
      }
      else if (menu.type === TypeaheadMenuOptionType.none) {
        throw new Error("not implemented");
      }
      else if (menu.type === TypeaheadMenuOptionType.header) {
        setCurrentMenu(TypeaheadMenuOptionType.none);
      }
      else if (menu.type === TypeaheadMenuOptionType.addRule) {
        //
      }
      else if (menu.type === TypeaheadMenuOptionType.web) {
        //
      }
      else if (menu.type === TypeaheadMenuOptionType.codebase) {
        //
      }
      else if (menu.type === TypeaheadMenuOptionType.knowledge) {
        //
      }
      else if (menu.type === TypeaheadMenuOptionType.slashCommand) {
        editor.update(() => {
          const structure = (menu as MentionTypeaheadOption<TypeaheadMenuOptionType.slashCommand>).structure;
          const mentionNode = $createMentionNodeV2({
            structure,
          });
          if (nodeToReplace) {
            nodeToReplace.replace(mentionNode);
            mentionNode.insertAfter($createTextNode(" "));
          }
        });
        closeMenu();
      }
      else {
        throwNeverError(menu.type);
      }
    },
    [reportCustomPromptRecentUsed, editor, repoPath.value],
  );

  const [haveSlashCommand, setHaveSlashCommand] = useState(false);

  useEffect(() => {
    return editor.registerTextContentListener(() => {
      editor.read(() => {
        let have = false;
        const walk = (node: LexicalNode) => {
          if ($isMentionNodeV2(node) && node.__structure.type === "slashCommand") {
            have = true;
            return;
          }
          else if ($isElementNode(node)) {
            node.getChildren().forEach(child => walk(child));
          }
        };
        walk($getRoot());
        setHaveSlashCommand(have);
      });
    });
  });

  const checkForMentionMatch = useCallback(
    (text: string) => {
      if (haveSlashCommand) {
        return null;
      }
      return mentionPanelMatcher.getPossibleQueryMatch(text);
    },
    [haveSlashCommand],
  );

  const { options } = useSlashOptions({
    currentMenu,
    queryString: queryString || "",
    mode,
  });

  const { role } = useUserInputTextAreaContext();

  const [menuResolution, setMenuResolution] = useState<MenuResolution>();

  const menuRenderFn: MenuRenderFn<MentionTypeaheadOption> = useCallback(function MenuRenderComponent(
    anchorElementRef,
    itemProps,
    matchingString,
  ) {
    return (
      <MenuRenderPortal
        role={role}
        menuResolution={menuResolution}
        anchorElementRef={anchorElementRef}
      >
        <TypeaheadMenuContext.Provider value={{ anchorElementRef, itemProps, matchingString, setCurrentMenu, currentMenu }}>
          <div className="typeahead-popover mentions-menu w-[400px] max-w-[90vw]" tabIndex={-1}>
            <MentionPanel
              selectMode="any"
              options={options}
              query={queryString || ""}
            />
          </div>
        </TypeaheadMenuContext.Provider>
      </MenuRenderPortal>
    );
  }, [currentMenu, menuResolution, options, queryString, role]);

  useEffect(() => {
    return editor.registerCommand(
      KEY_ESCAPE_COMMAND,
      () => {
        if (currentMenu !== TypeaheadMenuOptionType.none) {
          setCurrentMenu(TypeaheadMenuOptionType.none);
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_NORMAL,
    );
  }, [currentMenu, editor]);

  const mentionShownLatest = useLatest(slashV2Shown);

  useEffect(() => {
    return editor.registerRootListener((root) => {
      let listenerOutdated = false;
      const onBlur = () => {
        setTimeout(() => {
          if (!mentionShownLatest.current) {
            return;
          }
          if (listenerOutdated) {
            return;
          }
          const activeElement = DOM.getActiveElement();
          /*
            点击了编辑器和 MentionPanel 以外的元素
            https://team.corp.kuaishou.com/task/B2490391
             */
          const isClickedOutside = !(activeElement instanceof HTMLElement && activeElement.closest(".typeahead-popover"));
          if (isClickedOutside) {
            setCurrentMenu(TypeaheadMenuOptionType.none);
            const escEvent = new KeyboardEvent("keydown", {
              key: "Escape", // 按键名称
              code: "Escape", // 按键代码
              keyCode: 27, // 旧版浏览器中使用的键码
              which: 27, // 旧版浏览器中使用的键码
              bubbles: true, // 事件是否冒泡
              cancelable: true, // 事件是否可取消
            });
            setCurrentMenu(TypeaheadMenuOptionType.none);
            /* 手动触发 Mention 面板关闭 https://github.com/facebook/lexical/blob/v0.17.1/packages/lexical-react/src/shared/LexicalMenu.ts#L405 */
            editor.dispatchCommand(KEY_ESCAPE_COMMAND, escEvent);
          }
        }, 0);
      };
      root?.addEventListener("blur", onBlur);
      return () => {
        listenerOutdated = true;
        root?.removeEventListener("blur", onBlur);
      };
    });
  }, [editor, mentionShownLatest, setCurrentMenu]);

  useEffect(() => {
    httpClient.getCustomPrompts().then((res) => {
      setCustomPrompts(res);
    });
  }, [setCustomPrompts]);

  useEffect(() => {
    httpClient.getPlatformConfig().then((res) => {
      setPromptTemplate(res.promptConfigs);
      const status: DisableRichEditorMenu = {};
      res.promptConfigs.forEach((r) => {
        status[CommandPrefix.SLASH + r.key] = {
          status: false,
          msg: "",
        };
      });
      setDisabledMenu(status);
    });
  }, [setDisabledMenu, setPromptTemplate]);

  return (
    <BetterLexicalTypeaheadMenuPlugin<MentionTypeaheadOption>
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForMentionMatch}
      options={options}
      anchorClassName=" z-20"
      menuRenderFn={menuRenderFn}
      onOpen={(resolution) => {
        setMenuResolution(resolution);
        setSlashV2Shown(true);
      }}
      onClose={() => {
        setSlashV2Shown(false);
        setCurrentMenu(TypeaheadMenuOptionType.none);
      }}
    />
  );
});

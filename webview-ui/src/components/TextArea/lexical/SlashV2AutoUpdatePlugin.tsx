import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect } from "react";
import { useLexicalEditable } from "@lexical/react/useLexicalEditable";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $setSelection } from "lexical";
import { collectMentionNodeV2 } from "@/logics/UserInputTextarea/ContextHeader/collectMentionNode";
import { MentionNodeV2Structure_File, MentionNodeV2Structure_Selection, MentionNodeV2Structure_SlashCommand, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";

/**
 * 一些CustomVariable(file, selection) 需要根据当前文件和选中的内容自动更新
 * 需求 自定义提示词：https://docs.corp.kuaishou.com/k/home/<USER>/fcABCcPY44EYjgB1nEMJT8zWl
 *
 * 修复panel模式下currentFileAndSelection Observable不能正确更新的问题，通过移除webviewVisible的依赖条件并添加更好的错误处理
 * @returns
 */
export function SlashV2AutoUpdatePlugin() {
  const [editor] = useLexicalComposerContext();
  const editable = useLexicalEditable();
  useEffect(() => {
    // 移除webviewVisible的依赖，确保在panel模式下也能正常工作
    if (editable) {
      const subscription = kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection()
        .subscribe((data) => {
          try {
            const slashCommandNodesToBeUpdated = collectMentionNodeV2(editor)
              .filter(v =>
                v.__structure.type === "slashCommand"
                && slashCommandSetRequiringContextItem.has(v.__structure.command),
              );
            const contextItem: MentionNodeV2Structure_File | MentionNodeV2Structure_Selection | undefined = data?.range
              ? {
                  type: "selection",
                  content: data.content,
                  uri: data.uri,
                  range: data.range,
                  relativePath: data.relativePath,
                }
              : data?.uri?.trim()
                ? {
                    type: "file",
                    uri: data.uri,
                    relativePath: data.relativePath,
                  }
                : undefined;

            editor.update(() => {
              const isUpdated = slashCommandNodesToBeUpdated.length > 0;
              for (const node of slashCommandNodesToBeUpdated) {
                const structure = node.getStructureData() as MentionNodeV2Structure_SlashCommand;
                node.setStructureData({
                  ...structure,
                  contextItem,
                });
              }
              if (isUpdated) {
                $setSelection(null);
              }
            });
          }
          catch (error) {
            console.error("Error updating slash command nodes:", error);
          }
        });
      return () => {
        subscription.unsubscribe();
      };
    }
  }, [editable, editor]); // 移除webviewVisible依赖
  return null;
}

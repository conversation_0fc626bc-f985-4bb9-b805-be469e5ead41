import { InitialConfigType, InitialEditorStateType, LexicalComposer } from "@lexical/react/LexicalComposer";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { EditorRefPlugin } from "@lexical/react/LexicalEditorRefPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { Placeholder } from "./components/Placeholder";
import { useColorMode, useControllableState, useMergeRefs, useToast } from "@chakra-ui/react";
import { FocusPlugin } from "@/components/TextArea/lexical/FocusPlugin";
import {
  MutableRefObject,
  RefCallback,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  CommandType,
  DisableRichEditorMenu,
  SharpCommand,
} from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { RichEditorDisabledReason } from "@shared/constant";
import {
  $createMentionNode,
  MentionNode,
} from "@/components/TextArea/lexical/CommandNode";
import { DeletePlugin } from "@/components/TextArea/lexical/DeletePlugin";
import { EmptyNode } from "@/components/TextArea/lexical/EmptyNode";
import { SaveStatePlugin } from "@/components/TextArea/lexical/SaveStatePlugin";
import {
  $createTextNode,
  $insertNodes,
  COMMAND_PRIORITY_LOW,
  KEY_ESCAPE_COMMAND,
  LexicalEditor,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";

import { ClearPlugin } from "@/components/TextArea/lexical/ClearPlugin";
import { InsertLineBreakPlugin } from "@/components/TextArea/lexical/InsertLineBreakPlugin";
import { InsertCommandSign } from "@/components/TextArea/lexical/InsertCommandSign";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { EnterSendPlugin } from "@/components/TextArea/lexical/EnterSendPlugin";
import { CommandPluginRef } from "@/components/TextArea";
import { OptPlugin } from "@/components/TextArea/lexical/OptPlugin";
import { DialogSetting } from "@/store/record";
import eventBus from "@/utils/eventBus";
import repoChatService, { WorkspaceState } from "@/services/repo-chat";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { RichEditorContext } from "./hooks/useRichEditorContext";
import { twMerge } from "tailwind-merge";
import { CustomVariableNode } from "./lexical/CustomVariableNode";
import { CustomVariableAutoUpdatePlugin } from "./lexical/CustomVariableAutoUpdatePlugin";
import { useIdeEnv } from "@/hooks/useIdeEnv";

interface IProps {
  editorRef?: RefObject<LexicalEditor> | RefCallback<LexicalEditor>;
  panelRef: RefObject<HTMLDivElement>;
  optRef?: RefObject<HTMLDivElement>;
  moreOpt?: React.ReactNode;
  clearRef?: MutableRefObject<{ clear: () => void } | undefined>;
  insertSignRef?: MutableRefObject<
    { insertSign: (sign: string) => void } | undefined
  >;
  onSubmit: () => unknown;
  onStop?: () => unknown;
  onEscape?: () => unknown;
  changeEditorState: (
    state: SerializedEditorState<SerializedLexicalNode>
  ) => void;
  commandShown?: {
    sharpShown: boolean;
    slashShown: boolean;
  };
  onCommandShownChange?: (state: {
    sharpShown: boolean;
    slashShown: boolean;
  }) => void;
  sharpPluginRef?: MutableRefObject<CommandPluginRef | undefined>;
  slashPluginRef?: MutableRefObject<CommandPluginRef | undefined>;
  setting?: DialogSetting;
  disabled: boolean;
  className?: string;
  editorClassName?: string;
  customOptions?: {
    /**
     * @deprecated 参考新版助理模式
     */
    autoInsertCurrentFileCommand?: boolean;
    slashCommandEnabled?: boolean;
    sharpCommandEnabled?: boolean;
    uploadFileEnabled?: boolean;
    /** 过滤掉知识命令中某些menu的key */
    filterSharpCommandKeyList?: SharpCommand[];
  };
  placeholder?: string;
  focused?: boolean;
  onFocusedChange?: (focused: boolean) => void;
  initialEditorState?: InitialEditorStateType;
  editable?: boolean;
  namespace?: string;
  loading?: boolean;
}

export const RichEditor: React.FC<IProps> = (props: IProps) => {
  const {
    editorRef: editorRefProp,
    customOptions,
    onCommandShownChange,
    commandShown: commandShownProp,
    changeEditorState,
    clearRef,
    onSubmit,
    onStop,
    insertSignRef: insertSignRefProp,
    optRef,
    moreOpt,
    setting,
    disabled,
    initialEditorState,
    className,
    editorClassName,
    editable = true,
    namespace = "RichEditor",
    loading = false,
    onEscape: onEscapeProp,
  } = props;

  const insertSignRefInner = useRef<{ insertSign: (sign: string) => void }>();

  const insertSignRef = useMergeRefs(insertSignRefProp, insertSignRefInner);

  const sharpPluginRef = useRef<CommandPluginRef | undefined>();

  const richEditorPanelMenuStore = useRichEditPanelMenuStore();
  const updateCodeSearchWorkspaceInfo = useRichEditPanelMenuStore(s => s.updateCodeSearchWorkspaceInfo);
  const setDisabledMenu = useRichEditPanelMenuStore(state => state.setDisabledMenu);
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const editorRef = useRef<LexicalEditor | null>(null);
  const composedEditorRef = useMergeRefs(editorRefProp, editorRef);

  const [focused, setFocused] = useControllableState({
    value: props.focused,
    onChange: props.onFocusedChange,
    defaultValue: false,
  });

  const toast = useToast();
  const lexicalEditorErrorToastId = "LexicalEditorErrorToastId";

  const initialConfig = useMemo<InitialConfigType>(() => ({
    namespace: namespace,
    nodes: [MentionNode, EmptyNode, CustomVariableNode],
    onError: (error: any) => {
      if (!toast.isActive(lexicalEditorErrorToastId)) {
        toast({
          id: lexicalEditorErrorToastId,
          title: "渲染错误",
          description: "编辑器发生渲染错误， 请确认插件版本为最新",
          status: "error",
          duration: 3000,
          isClosable: true,
          position: "bottom",
        });
        console.error(error);
      }
    },
    editorState: initialEditorState,
    editable,
  }), [editable, initialEditorState, namespace, toast]);

  const fetchCurrentFilAndCodeBaseRepo = useCallback(async () => {
    const { filePath, repoPath }
      = await repoChatService.getCurrentFilePathAndRepoPath();
    richEditorPanelMenuStore.updateCodeBasePath(repoPath || "");
    richEditorPanelMenuStore.updateCurrentFilePath(filePath || "");
    richEditorPanelMenuStore.setDisabledMenu({
      [SharpCommand.CURRENT_FILE]: {
        status: !filePath,
        msg: !filePath ? RichEditorDisabledReason.unopenedFile : "",
      },
    });

    // NOTE: 首次加载时需要依赖 currentFilePath 插入当前文件命令，所以在这里执行
    if (customOptions?.autoInsertCurrentFileCommand) {
      insertCurrentFileCommand();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customOptions?.autoInsertCurrentFileCommand]);
  useEffect(() => {
    kwaiPilotBridgeAPI.onActiveTextChange((data) => {
      const {
        document: { relativePath },
      } = data;
      // 更新当前文件
      richEditorPanelMenuStore.updateCurrentFilePath(relativePath || "");
      richEditorPanelMenuStore.setDisabledMenu({
        [SharpCommand.CURRENT_FILE]: {
          status: !relativePath,
          msg: !relativePath ? RichEditorDisabledReason.unopenedFile : "",
        },
      });
      if (relativePath) {
        repoChatService.currentFilePath = relativePath;
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
          [relativePath],
        );
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
          [repoChatService.handleFilePathToDir(relativePath)],
        );
      }
    });
  }, [richEditorPanelMenuStore]);

  useEffect(() => {
    updateCodeSearchWorkspaceInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    fetchCurrentFilAndCodeBaseRepo();
  }, [fetchCurrentFilAndCodeBaseRepo]);

  useEffect(() => {
    const disableMenuListener = (data: DisableRichEditorMenu) => {
      richEditorPanelMenuStore.setDisabledMenu(data);
    };

    eventBus.on("pushRichEditorPanelDisableMenu", disableMenuListener);

    return () => {
      eventBus.off("pushRichEditorPanelDisableMenu", disableMenuListener);
    };
  }, [richEditorPanelMenuStore]);

  const insertCurrentFileCommand = useCallback(async () => {
    const editor = editorRef.current;
    if (!editor) return;
    const currentFilePath = repoChatService.currentFilePath;
    if (!currentFilePath) return;
    const title = currentFilePath.split("/").pop() || "";
    editor.update(() => {
      const commandNode = $createMentionNode(
        `#${title}`,
        {
          key: SharpCommand.CURRENT_FILE,
          commandType: CommandType.SHARP,
          title,
          description: "",
          type: "normal",
          data: currentFilePath,
          uri: "",
        },
        isDark,
      );
      $insertNodes([commandNode, $createTextNode(" ")]);
    });
  }, [editorRef, isDark]);

  useEffect(() => {
    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      if (data.ide !== "kwaipilot-xcode") {
        setDisabledMenu({
          [SharpCommand.FILE]: {
            status: false,
            msg: "",
          },
        });
      }
    });
  }, [setDisabledMenu]);

  const [commandShown, setCommandShown] = useControllableState({
    value: commandShownProp,
    onChange: onCommandShownChange,
    defaultValue: {
      sharpShown: false,
      slashShown: false,
    },
  });

  useEffect(() => {
    return editorRef.current?.registerCommand(KEY_ESCAPE_COMMAND, () => {
      onEscapeProp?.();
      return true;
    }, COMMAND_PRIORITY_LOW);
  }, [onEscapeProp]);

  // 普通对话还没有新版只是，先放这里
  const [mentionShown, setMentionShown] = useState(false);
  const [slashV2Shown, setSlashV2Shown] = useState(false);

  const [, isKwaiPilotIDE] = useIdeEnv();

  const handleKeyDownInIde = useCallback((event: React.KeyboardEvent) => {
    // 在 ide 内渲染时，ctrl+a, ctrl+z, ctrl+shift+z等快捷键会被文本编辑器处理，默认阻止事件冒泡
    const isMacOS = navigator.platform.toUpperCase().indexOf("MAC") >= 0;
    const modifier = isMacOS ? event.metaKey : event.ctrlKey;

    if (modifier) {
      const key = event.key.toLowerCase();
      // 对于command+a (全选), command+z (撤销), command+shift+z (重做)等，
      if (key === "a" || key === "z") {
        event.stopPropagation();
      }
    }
  }, []);

  return (
    <div
      className={className}
    >
      <RichEditorContext.Provider value={{
        focused,
        setFocused,
        commandShown,
        setCommandShown,
        mentionShown,
        setMentionShown,
        slashV2Shown,
        setSlashV2Shown,
      }}
      >
        <LexicalComposer initialConfig={initialConfig}>
          <RichTextPlugin
            contentEditable={(
              <ContentEditable
                className={twMerge(
                  "w-full text-[13px] leading-[20px] pl-3 pr-2 pt-2 z-10 cursor-text relative focus-visible:outline-none text-[var(--custom-text-common)]",
                  editorClassName,
                )}
                style={{
                  outline: "none",
                  fontFamily: "PingFang SC, PingFang SC-Regular",
                }}
                onKeyDown={isKwaiPilotIDE ? handleKeyDownInIde : undefined}
              >
              </ContentEditable>
            )}
            placeholder={<Placeholder text={props.placeholder} />}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <EditorRefPlugin editorRef={composedEditorRef} />
          <HistoryPlugin />
          <AutoFocusPlugin />
          <FocusPlugin changeFocused={setFocused} />
          <EnterSendPlugin submit={onSubmit} />
          <DeletePlugin />
          <SaveStatePlugin changeEditorState={changeEditorState} />
          <ClearPlugin ref={clearRef} />
          <InsertLineBreakPlugin />
          <InsertCommandSign focused={focused} ref={insertSignRef} />
          {optRef && (
            <OptPlugin
              loading={loading}
              optRef={optRef}
              moreOpt={moreOpt}
              setting={setting}
              onSubmit={onSubmit}
              onStop={onStop}
              disabled={disabled}
              sharpPluginRef={sharpPluginRef}
              focused={focused}
              uploadFileEnabled={customOptions?.uploadFileEnabled}
            >
            </OptPlugin>
          )}
          <CustomVariableAutoUpdatePlugin />
        </LexicalComposer>
      </RichEditorContext.Provider>
    </div>
  );
};

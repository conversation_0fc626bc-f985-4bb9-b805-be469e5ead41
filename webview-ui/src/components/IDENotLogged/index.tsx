import { Box, Text, Button } from "@chakra-ui/react";

interface UnloginAIAssistantPanelProps {
  onLogin?: () => void;
}

export function IDENotLogged({ onLogin }: UnloginAIAssistantPanelProps) {
  const handleLogin = () => {
    if (onLogin) {
      onLogin();
    }
  };

  return (
    <Box className="ai-assistant-container">
      <Box className="no_login main-frame">
        <Box className="sub-frame">
          <Box className="inner-frame">
            <Box className="pilot-section">
              <Text className="pilot-feature-primary">
                Collaboration with
                {" "}
                <Text as="span" className="pilot-feature-secondary">
                  Kwaipilot
                </Text>
              </Text>
            </Box>
            <Box className="speed-section">
              Code Smarter, Build Faster
            </Box>
          </Box>
          <Button
            className="action-button"
            data-button-type="login"
            onClick={handleLogin}
          >
            <Text className="button-label">Log in</Text>
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

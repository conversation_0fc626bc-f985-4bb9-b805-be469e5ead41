import { useEffect, useState } from "react";
import {
  getIcon,
  loadIcon,
  replaceIDs,
  Icon as OriginIcon,
  type IconifyIcon,
  type IconProps,
} from "@iconify/react";
import { useTrustedHTML } from "../../../hooks/useTrustedHTML";

function Icon(props: IconProps) {
  const { icon, ...restProps } = props;
  const [iconData, setIconData] = useState<Required<IconifyIcon> | null>(() => {
    return typeof icon === "string" ? getIcon(icon) ?? null : null;
  });

  useEffect(() => {
    if (typeof icon === "string") {
      // 首先尝试从内存中获取
      const cachedIcon = getIcon(icon);
      if (cachedIcon) {
        setIconData(cachedIcon);
        return;
      }

      // 如果内存中没有，则从 API 加载
      loadIcon(icon).then((data) => {
        if (data) {
          setIconData(data);
        }
      });
    }
  }, [icon]);

  const trustedHTMLRef = useTrustedHTML(replaceIDs(iconData?.body || ""));

  if (typeof icon !== "string") {
    return <OriginIcon {...props} />;
  }

  if (!iconData) {
    return <span></span>;
  }

  // 自行构建 SVG 元素
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox={`0 0 ${iconData.width} ${iconData.height}`}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      ref={trustedHTMLRef}
      {...restProps}
    />
  );
}

export { Icon };

import LineIcon from "@/assets/line.svg?react";
import CopyIcon from "@/assets/copy.svg?react";
import LikeIcon from "@/assets/like.svg?react";
import DislikeIcon from "@/assets/dislike.svg?react";

import { SingleIcon } from "@/components/SingleIcon";
import { ModelSelector } from "@/components/TextArea/components/ModelSelector";
import { IChatModelType } from "@shared/types/business";
import clsx from "clsx";

import { Tooltip } from "@/components/Union/chakra-ui";
import { chakra } from "@chakra-ui/react";

import { Icon } from "@/components/Union/t-iconify";
import { ICachedMessageAnswer } from "@shared/types/chatHistory";
import { AUTO_THINK_MODEL_SET, THINK_MODEL_SET } from "shared/lib/business";
import { useMemo } from "react";

type ActionsProps = {
  total: number;
  currentAnswer: ICachedMessageAnswer;
  current: number;
  handlePre: () => void;
  handleNext: () => void;
  onResend: (modelType?: IChatModelType) => void;
  onContinue: () => void;
  onLikeOrUnlike: (isLike?: boolean) => void;
  likeStatu?: "like" | "unlike" | "cancel";
  onCopy: () => void;
  isLast?: boolean;
};

export const Actions = (props: ActionsProps) => {
  const {
    total,
    current,
    handleNext,
    handlePre,
    onResend,
    onContinue,
    onLikeOrUnlike,
    likeStatu,
    onCopy,
    isLast,
    currentAnswer,
  } = props;
  // 渲染一个自带统一样式的 单icon 24 * 24
  const selectModelCallback = (modelType: IChatModelType) => {
    onResend(modelType);
  };
  const isThinkModel = useMemo(() => AUTO_THINK_MODEL_SET.has(currentAnswer.modelType) || THINK_MODEL_SET.has(currentAnswer.modelType), [currentAnswer.modelType]);
  return (
    <div className="px-[8px] flex gap-6">
      {currentAnswer.finishReason === "length" && !isThinkModel && (
        <Tooltip label="输出过长，继续获取更多结果" openDelay={500} placement="bottom-start">
          <chakra.button
            className="text-foreground text-[12px] px-1 h-[26px] flex items-center gap-1 rounded bg-transparent border border-solid border-commandCenter-inactiveBorder hover:bg-toolbar-hoverBackground"
            onClick={onContinue}
          >
            <Icon icon="solar:restart-outline" width="16" height="16" />
            <span className="whitespace-nowrap">继续生成</span>
          </chakra.button>
        </Tooltip>
      )}
      <div className="flex gap-[6px] ml-auto">
        {total > 1 && (
          <div className="flex gap-[4px]">
            <SingleIcon onClick={handlePre} title="上一条">
              <Icon icon="codicon:chevron-left" width="16" height="16" />
            </SingleIcon>
            <div className=" text-foreground text-[13px] leading-[19.5px]  flex">
              <div className="min-w-[8px] flex items-center justify-center">
                {current}
              </div>
              <div className="flex items-center justify-center ">/</div>
              <div className="min-w-[8px] flex items-center justify-center">
                {total}
              </div>
            </div>
            <SingleIcon onClick={handleNext} title="下一条">
              <Icon icon="codicon:chevron-right" width="16" height="16" />
            </SingleIcon>
          </div>
        )}
        <SingleIcon onClick={onCopy} title="复制">
          <CopyIcon className="text-icon-foreground" />
        </SingleIcon>

        <ModelSelector
          placement={isLast ? "top-start" : "bottom-start"}
          trigger="click"
          selectModelCallback={selectModelCallback}
          className="px-0 py-0 hover:bg-toolbar-hoverBackground"
          activeBgColor="bg-toolbar-hoverBackground"
        >
          <Tooltip openDelay={500} label="重新生成">

            <div className="flex cursor-pointer items-center h-[24px] gap-[4px] text-icon-foreground px-[2px] py-[4px] rounded-[4px]">
              <Icon icon="codicon:sync" width="16" height="16" />
            </div>
          </Tooltip>
        </ModelSelector>
        <div className="flex items-center text-commandCenter-inactiveBorder">
          <LineIcon />
        </div>
        <div className="flex gap-[6px]">
          <SingleIcon
            title="喜欢"
            active={likeStatu === "like"}
            onClick={() => {
              likeStatu === "like"
                ? onLikeOrUnlike(undefined)
                : onLikeOrUnlike(true);
            }}
          >
            <LikeIcon
              className={clsx("translate-y-[-1px] text-icon-foreground")}
            />
          </SingleIcon>
          <SingleIcon
            title="不喜欢"
            active={likeStatu === "unlike"}
            onClick={() => {
              likeStatu === "unlike"
                ? onLikeOrUnlike(undefined)
                : onLikeOrUnlike(false);
            }}
          >
            <DislikeIcon className="translate-y-[1px] text-icon-foreground" />
          </SingleIcon>
        </div>
      </div>
    </div>
  );
};

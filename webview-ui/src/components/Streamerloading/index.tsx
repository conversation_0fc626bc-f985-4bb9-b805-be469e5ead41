import { getRootContainer } from "@/utils/dom";
import "./index.css";
import { useEffect } from "react";

interface Props {
  children: React.ReactNode;
  isLoading: boolean;
  className?: string;
  id: string;
}

export const StreamLoading = ({
  children,
  isLoading,
  className,
  id,
}: Props) => {
  useEffect(() => {
    // https://stackoverflow.com/questions/20306204/using-queryselector-with-ids-that-are-numbers
    const e = getRootContainer()?.querySelector?.(`[id='${id}'`);
    if (e && isLoading) {
      e.classList.add("stream-loading");
    }
    else {
      e?.classList.remove("stream-loading");
    }
  }, [isLoading, id]);
  return (
    <div className={`stream-loading-container rounded-lg ${className}`} id={id}>
      <div className="rounded-[6.5px] shadow-assisant-message max-w-full">
        {children}
      </div>
    </div>
  );
};

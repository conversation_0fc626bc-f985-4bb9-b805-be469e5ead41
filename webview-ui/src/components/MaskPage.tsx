import { kwaiPilotBridgeAPI } from "@/bridge";
import React, { useMemo } from "react";
import DarkMaskMainIcon from "@/assets/mask-main-dark.svg?react";
import DarkMaskBgIcon from "@/assets/mask-bg-dark.svg?react";
import LightMaskMainIcon from "@/assets/mask-main-light.svg?react";
import LightMaskBgIcon from "@/assets/mask-bg-light.svg?react";
import { useColorMode } from "@chakra-ui/react";

const MaskPage: React.FC = () => {
  const { colorMode: theme } = useColorMode();
  const isDarkMode = useMemo(() => theme === "dark", [theme]);
  return (
    <div className="w-full h-screen bg-sideBar-background flex items-center justify-center relative overflow-hidden">
      {/* 背景装饰图标 */}
      <div className="absolute inset-0 flex items-center justify-center opacity-70">
        {isDarkMode ? <DarkMaskBgIcon className="w-96 h-96 object-cover rotate-45" /> : <LightMaskBgIcon className="w-96 h-96 object-cover rotate-45" />}
      </div>

      {/* 主要内容 */}
      <div className="text-center z-10 relative px-8">
        {/* 主图标 */}
        <div className="flex items-center justify-center">
          {isDarkMode ? <DarkMaskMainIcon className="w-20 h-20 opacity-90" /> : <LightMaskMainIcon className="w-20 h-20 opacity-90" />}
        </div>

        {/* 标题 */}
        <div className="text-lg font-medium text-[var(--vscode-foreground)] ">
          存在其他窗口
        </div>

        {/* 描述文字 */}
        <div className="text-sm text-[var(--vscode-descriptionForeground)] leading-relaxed">
          Kwaipilot 插件已在编辑器中打开
        </div>

        {/* 操作按钮 */}
        <button
          onClick={() => kwaiPilotBridgeAPI.view.revealPanel()}
          className="text-[var(--vscode-textLink-foreground)] text-sm hover:text-text-brand-hover transition-colors cursor-pointer "
        >
          打开对应标签页
        </button>
      </div>
    </div>
  );
};

export default MaskPage;

import { useNavigate } from "react-router-dom";
import { Popover } from "@/components/Union/chakra-ui";

import baseInfoManager from "@/utils/baseInfo";
import { useUserStore } from "@/store/user";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useState, useEffect, useCallback } from "react";
import { reportUserAction } from "@/utils/weblogger";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { useViewModel } from "@/hooks/useViewModel";

import BetaIcon from "@/assets/beta-composer.svg?react";
import { useIdeEnv } from "@/hooks/useIdeEnv";
import { SingleIcon } from "../SingleIcon";
import { Icon } from "../Union/t-iconify";
import { PopoverContent, PopoverTrigger, useToast } from "@chakra-ui/react";
import { vsCss } from "@/style/vscode";

function useDeveloperModeDisclosure(): {
  tap(): void;
  isDeveloperMode: boolean;
} {
  const MAX_TAP_DURATION = 500;
  const MAX_TAP_COUNT = 10;

  const toast = useToast();
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);
  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode") ?? false;

  useEffect(() => {
    if (clickCount >= MAX_TAP_COUNT) {
      kwaiPilotBridgeAPI.extensionDeveloper.$setIsDeveloperMode(!isDeveloperMode)
        .then(() => {
          toast({
            title: `开发者模式已${isDeveloperMode ? "关闭" : "开启"}`,
            status: "success",
          });
        });
      setClickCount(0);
    }
  }, [clickCount, isDeveloperMode, toast]);

  const tap = () => {
    const now = Date.now();
    if (now - lastClickTime > MAX_TAP_DURATION) {
      setClickCount(1);
    }
    else {
      setClickCount(prev => prev + 1);
    }
    setLastClickTime(now);
  };

  return {
    tap,
    isDeveloperMode,
  };
}

interface ViewModelChangeProps {
  shouldBlock?: () => boolean;
  onBlockedContinue?: () => void;
  hasIndeterminatedWorkingSet?: boolean;
}

function ViewModelChange({ shouldBlock, onBlockedContinue, hasIndeterminatedWorkingSet }: ViewModelChangeProps) {
  const [fullScreenBlocked, setFullScreenBlocked] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // 使用优化后的 useViewModel hook
  const { isPanel } = useViewModel();

  const handleFullScreen = useCallback((newWindow: boolean) => {
    const action = () => kwaiPilotBridgeAPI.extensionWebview.$moveFullMode(newWindow);

    if (shouldBlock?.()) {
      setPendingAction(() => action);
      setFullScreenBlocked(true);
      return;
    }
    action();
  }, [shouldBlock]);

  const handleSiderbar = useCallback(() => {
    const action = () => kwaiPilotBridgeAPI.extensionWebview.$moveToSidebar();

    if (shouldBlock?.()) {
      setPendingAction(() => action);
      setFullScreenBlocked(true);
      return;
    }
    action();
  }, [shouldBlock]);

  const handleBlockedContinue = useCallback(() => {
    onBlockedContinue?.();
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
    setFullScreenBlocked(false);
  }, [onBlockedContinue, pendingAction]);

  const handleBlockedCancel = useCallback(() => {
    setPendingAction(null);
    setFullScreenBlocked(false);
  }, []);

  return (
    <>
      {isPanel
        ? (
            <SingleIcon className="size-[20px]" onClick={handleSiderbar} title="复原窗口至侧边栏">
              <Icon icon="mynaui:arrow-down-right-square" />
            </SingleIcon>
          )
        : (
            <Popover
              placement="bottom-end"
              closeOnBlur={true}
            >
              <PopoverTrigger>
                <div
                  className="cursor-pointer hover:bg-toolbar-hoverBackground w-[24px] h-[24px] rounded-[4px] flex items-center justify-center size-[20px]"
                >
                  <Icon icon="codicon:ellipsis" />
                </div>
              </PopoverTrigger>
              <PopoverContent w="120px" rounded="4px" p="2px" bg={vsCss.dropdownBackground} boxShadow="none">
                <div
                  className="w-full rounded-sm px-[8px] py-[4px] relative hover:bg-list-dropBackground cursor-pointer"
                  onClick={() => handleFullScreen(false)}
                >
                  在编辑器内打开
                </div>
                <div
                  className="w-full rounded-sm px-[8px] py-[4px] relative hover:bg-list-dropBackground cursor-pointer"
                  onClick={() => handleFullScreen(true)}
                >
                  在新窗口打开
                </div>
              </PopoverContent>
            </Popover>
          )}
      {/* 阻断确认对话框 */}
      {fullScreenBlocked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-editor-background border border-border-common rounded-lg p-4 max-w-md">
            <div className="flex items-center gap-2 mb-3">
              <Icon icon="codicon:warning" className="text-editorWarning-background" />
              <h3 className="text-text-common-primary font-medium text-[13px]">
                {hasIndeterminatedWorkingSet ? "存在未处理代码变更" : "回答生成中"}
              </h3>
            </div>
            <p className="text-text-common-secondary text-[13px] mb-4">
              {hasIndeterminatedWorkingSet
                ? "存在未处理的代码变更，此时切换视图会导致代码变更丢失，是否仍要继续切换？"
                : "此时切换视图会导致当前回答的生成被中断，是否仍要继续切换？"}
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={handleBlockedCancel}
                className="px-3 py-1 text-[12px] text-text-common-secondary bg-bg-scrollbar-default rounded hover:bg-list-dropBackground"
              >
                取消
              </button>
              <button
                onClick={handleBlockedContinue}
                className="px-3 py-1 text-[12px] text-editor-background bg-text-common-primary rounded hover:opacity-80"
              >
                继续
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export const HistoryBar = ({
  current = "chat",
  action,
  shouldBlockViewChange,
  onBlockedViewChangeContinue,
  hasIndeterminatedWorkingSet,
}: {
  // TODO: 根据路由自动匹配 current
  current?: "chat" | "history" | "composer" | "composer-v2";
  action?: React.ReactNode;
  shouldBlockViewChange?: () => boolean;
  onBlockedViewChangeContinue?: () => void;
  hasIndeterminatedWorkingSet?: boolean;
}) => {
  const navigate = useNavigate();
  const userInfo = useUserStore(state => state.userInfo);
  const { tap, isDeveloperMode } = useDeveloperModeDisclosure();
  const [,isKwaiPilotIDE] = useIdeEnv();

  return (
    <div className="flex justify-between w-full h-9 items-center px-[12px] py-[7px] leading-[22px] text-[13px]" onClick={tap}>
      <div className="flex gap-[16px] items-center">
        {!baseInfoManager.isXcode && (
          <div
            onClick={() => {
              reportUserAction({
                key: "compose_agent_show",
                type: undefined,
              });
              navigate("/composer-v2");
            }}
            className={`hover:text-tab-activeForeground cursor-pointer relative ${
              current === "composer-v2"
                ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:bg-tab-activeForeground after:w-12 after:rounded-[12px_12px_2px_2px]`
                : "text-tab-inactiveForeground"
            }`}
          >
            <div className="flex items-center gap-[2px]">
              <span className="group-hover:text-text-common-primary">智能体</span>
              {!isKwaiPilotIDE && <BetaIcon />}
            </div>
          </div>
        )}
        <div
          onClick={() => {
            navigate("/chat");
          }}
          className={`hover:text-tab-activeForeground cursor-pointer relative ${
            current === "chat"
              ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:w-7 after:rounded-[12px_12px_2px_2px] after:bg-tab-activeForeground`
              : "text-tab-inactiveForeground"
          }`}
        >
          问答
        </div>
        {/* 不登录则不展示历史记录页面 */}
        {userInfo && (
          <div
            onClick={() => {
              navigate("/history");
            }}
            className={`hover:text-tab-activeForeground cursor-pointer relative ${
              current === "history"
                ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:bg-tab-activeForeground after:left-1/2 after:transform after:-translate-x-1/2 after:w-7 after:rounded-[12px_12px_2px_2px]`
                : "text-tab-inactiveForeground"
            }`}
          >
            历史
          </div>
        )}
        {isDeveloperMode && (
          <>
            <div className="text-text-common-tertiary font-semibold text-xs italic select-none">开发者模式</div>
          </>
        )}
      </div>
      <div className="flex gap-2 items-center justify-center">
        {action}

        {
          isKwaiPilotIDE
            ? null
            : (
                <ViewModelChange
                  shouldBlock={shouldBlockViewChange}
                  onBlockedContinue={onBlockedViewChangeContinue}
                  hasIndeterminatedWorkingSet={hasIndeterminatedWorkingSet}
                />
              )
        }
      </div>
    </div>
  );
};

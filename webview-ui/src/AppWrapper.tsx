import React from "react";
import App from "@/App";
import MaskPage from "@/components/MaskPage";
import { useViewModel } from "@/hooks/useViewModel";

// Create AppWrapper component, which listens to viewModel state and decides to render App or MaskPage
const AppWrapper: React.FC = () => {
  const { viewModel } = useViewModel();
  const showMask = (viewModel || "siderbar") !== window.__KWAIPILOT_VIEW_TYPE__;
  return showMask ? <MaskPage /> : <App />;
};

export default AppWrapper;

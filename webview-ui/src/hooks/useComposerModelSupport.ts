import { useMemo, useEffect, useState } from "react";
import { useComposerState } from "@/logics/composer/context/ComposerStateContext";
import { httpClient } from "@/http";
import { ClientSideModel } from "@/store/record";
import { cacheRequest } from "@/utils/cache-request";

/**
 * 获取 composer 模型列表（带缓存）
 */
async function getComposerModelList(): Promise<ClientSideModel[]> {
  return cacheRequest(
    async () => {
      const AUTO_THINK_MODEL = "kwaipilot_40b";
      const models = await httpClient.getComposerModelList();
      return models.map(model => ({
        ...model,
        isAutoThinkModel: model.modelType === AUTO_THINK_MODEL,
      }));
    },
    {
      key: "composer-model-list",
      ttl: 10 * 60 * 1000, // 10分钟缓存
      retryCount: 2, // 重试2次
      retryDelay: 1000, // 重试间隔1秒
    },
  );
}

/**
 * 用于管理 composer 模式下的模型支持功能
 * 包括获取模型列表和检查当前模型是否支持图片
 */
export function useComposerModelSupport() {
  const { userPreferredModel: currentModel } = useComposerState();
  const [modelList, setModelList] = useState<ClientSideModel[]>([]);

  // 获取模型列表
  useEffect(() => {
    getComposerModelList()
      .then(setModelList)
      .catch((error) => {
        console.error("获取模型列表失败:", error);
        setModelList([]);
      });
  }, []);

  // 获取当前模型详情
  const currentModelDetail = useMemo(() => {
    if (!currentModel || modelList.length === 0) {
      return null;
    }
    return modelList.find(model => model.modelType === currentModel) || null;
  }, [currentModel, modelList]);

  // 综合考虑当前模型的 supportImage 属性
  const allowImage = useMemo(() => {
    // 如果当前模型明确不支持图片，返回 false
    if (currentModelDetail && currentModelDetail.supportImage === false) {
      return false;
    }

    // 如果当前模型支持图片或者 supportImage 未定义（默认支持），返回 true
    return true;
  }, [currentModelDetail]);

  return {
    modelList,
    currentModelDetail,
    allowImage,
  };
}

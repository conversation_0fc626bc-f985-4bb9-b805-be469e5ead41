import { useCallback } from "react";

export interface ViewChangeBlockerConfig {
  shouldBlock?: () => boolean;
  onBlockedContinue?: () => void;
  hasIndeterminatedWorkingSet?: boolean;
}

export function useViewChangeBlocker(config: ViewChangeBlockerConfig) {
  const { shouldBlock, onBlockedContinue, hasIndeterminatedWorkingSet } = config;

  const shouldBlockViewChange = useCallback(() => {
    return shouldBlock?.() ?? false;
  }, [shouldBlock]);

  const onBlockedViewChangeContinue = useCallback(() => {
    onBlockedContinue?.();
  }, [onBlockedContinue]);

  return {
    shouldBlockViewChange,
    onBlockedViewChangeContinue,
    hasIndeterminatedWorkingSet: hasIndeterminatedWorkingSet ?? false,
  };
}

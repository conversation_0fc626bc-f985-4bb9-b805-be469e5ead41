import { kwaiPilotBridgeAPI } from "@/bridge";
import { IdeEnvContext } from "@/providers/IdeEnvProvider";
import { useContext, useEffect, useState, useRef } from "react";

export type KwaipilotIdeLabel = "kwaipilot-vscode" | "kwaipilot-ide" | "kwaipilot-xcode";

export function useIdeEnv() {
  const context = useContext(IdeEnvContext);
  if (context === undefined) {
    throw new Error("useIdeEnv must be used within an IdeEnvProvider");
  }

  const fetched = useRef(false);
  const [ide, setIde] = useState<KwaipilotIdeLabel>(
    "kwaipilot-vscode",
  );

  useEffect(() => {
    if (fetched.current) return;
    fetched.current = true;
    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      if (data.ide) {
        setIde(data.ide as KwaipilotIdeLabel);
      }
    });
  }, []);

  return [ide, context.isKwaiPilotIDE] as [
    KwaipilotIdeLabel,
    boolean,
  ];
}

import { useEffect } from "react";
import { useViewModelStore } from "@/store/viewModel";

/**
 * 获取当前的 viewModel 状态
 * 自动初始化 store 并返回当前状态
 */
export function useViewModel() {
  const { viewModel, initialize, isInitialized } = useViewModelStore();

  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [initialize, isInitialized]);

  return {
    viewModel,
    isSiderbar: viewModel === "siderbar",
    isPanel: viewModel === "panel",
    isInitialized,
  };
}

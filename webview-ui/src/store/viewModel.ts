import { create } from "zustand";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ViewModel } from "shared";

interface ViewModelState {
  viewModel: ViewModel;
  isInitialized: boolean;
  setViewModel: (viewModel: ViewModel) => void;
  initialize: () => void;
}

// 全局订阅实例，避免重复订阅
let globalSubscription: any = null;

export const useViewModelStore = create<ViewModelState>((set, get) => ({
  viewModel: "siderbar", // 默认值为 "siderbar"
  isInitialized: false,

  setViewModel: (viewModel) => {
    set({ viewModel });
  },

  initialize: () => {
    const state = get();

    // 如果已经初始化过，直接返回
    if (state.isInitialized || globalSubscription) {
      return;
    }

    // 创建全局订阅
    globalSubscription = kwaiPilotBridgeAPI.observableAPI
      .viewModel()
      .subscribe((newViewModel) => {
        set({ viewModel: newViewModel, isInitialized: true });
      });

    set({ isInitialized: true });
  },
}));

// 清理函数，可以在需要时调用
export const cleanupViewModelSubscription = () => {
  if (globalSubscription) {
    globalSubscription.unsubscribe();
    globalSubscription = null;
  }
};

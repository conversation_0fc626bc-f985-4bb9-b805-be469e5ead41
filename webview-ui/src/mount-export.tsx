// NOTE: ide 组件入口，不影响插件逻辑
import { kwaiPilotBridgeAPI } from "@/bridge-export";
import { VSCodeNativeBridge } from "@/bridge-export/kwaipilotBridge";
import "./mount-export.css";
import { initHighlighterInstance } from "./utils/highlighter";
import type Radar from "@ks-radar/radar";

// todo 临时
window.__KWAIPILOT_VIEW_TYPE__ = "siderbar";

/**
 * Sidebar UI 挂载信息
 */
interface ISidebarMountInfo {
  /** 挂载的UI类型 */
  uiType: string;
  /** 是否已挂载 */
  mounted: boolean;
  /** 挂载时间戳 */
  timestamp: number;
  /** 额外的挂载参数 */
  params?: Record<string, any>;
}

/**
 * Sidebar 操作方法的定义
 */
interface ISidebarAction {
  /** 操作的唯一标识 */
  id: string;
  /** 操作名称 */
  name: string;
  /** 操作描述 */
  description?: string;
  /** 操作的处理函数 */
  handler: (...args: any[]) => any;
  /** 操作的上下文 */
  context?: any;
}

/**
 * Side Action Service 接口
 * 用于管理 sidebar 内的UI挂载和方法注册
 */
interface ISideActionService {
  /**
  * 当前UI挂载信息，如果未挂载则为 undefined
  */
  readonly mountedUI: ISidebarMountInfo | undefined;

  /**
  * 当前已注册的sidebar操作
  */
  readonly registeredActions: readonly ISidebarAction[];

  /**
  * 挂载UI到sidebar
  * @param mountInfo UI挂载信息
  * @returns 是否挂载成功
  */
  /**
  * 挂载UI到sidebar
  * @param mountInfo UI挂载信息
  * @returns 是否挂载成功
  */
  mountUI(mountInfo: Omit<ISidebarMountInfo, "mounted" | "timestamp">): boolean;

  /**
  * 卸载UI
  * @returns 是否卸载成功
  */
  unmountUI(): boolean;

  /**
  * 检查UI是否已挂载
  * @returns 是否已挂载
  */
  isUIMounted(): boolean;

  /**
  * 注册sidebar操作方法
  * @param action 操作定义
  * @returns 是否注册成功
  */
  registerAction(action: ISidebarAction): boolean;

  /**
  * 注销sidebar操作方法
  * @param actionId 操作ID
  * @returns 是否注销成功
  */
  unregisterAction(actionId: string): boolean;

  /**
  * 执行已注册的操作
  * @param actionId 操作ID
  * @param args 操作参数
  * @returns 操作执行结果
  */
  executeAction(actionId: string, ...args: any[]): Promise<any>;

  /**
  * 获取已注册的操作
  * @param actionId 操作ID
  * @returns 操作定义，如果未找到则返回undefined
  */
  getAction(actionId: string): ISidebarAction | undefined;

  /**
  * 检查操作是否已注册
  * @param actionId 操作ID
  * @returns 是否已注册
  */
  hasAction(actionId: string): boolean;

  /**
  * 清空所有注册的操作
  */
  clearAllActions(): void;

  /**
  * 获取服务状态信息
  * @returns 包含挂载UI和注册操作数量的状态信息
  */
  getServiceStatus(): {
    isUIMounted: boolean;
    registeredActionCount: number;
    mountedUI: ISidebarMountInfo | undefined;
    registeredActions: readonly ISidebarAction[];
  };
}

/**
 * 预准备的常用 VSCode Services 接口
 * 在 contribution 中直接准备好具体的服务实例，避免通过字符串 ID 查找
 */
export interface VSCodeServicesAccessor {
  // 命令服务
  commandService?: {
    executeCommand(commandId: string, ...args: any[]): Promise<any>;
  };

  // 配置服务
  configurationService?: {
    getValue<T>(section: string): T | undefined;
    updateValue(section: string, value: any): Promise<void>;
  };

  // 日志服务
  logService?: {
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string | Error, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
  };

  // 主题服务
  themeService?: {
    getColorTheme(): {
      id: string;
      type: string;
      label: string;
    };
  };

  // 存储服务
  storageService?: {
    get(key: string, scope: number): string | undefined;
    store(key: string, value: string, scope: number): void;
    remove(key: string, scope: number): void;
  };

  // 通知服务
  notificationService?: {
    info(message: string): void;
    warn(message: string): void;
    error(message: string): void;
  };

  // 视图服务
  viewsService?: {
    openView(viewId: string, focus?: boolean): Promise<any>;
    openViewContainer(viewContainerId: string, focus?: boolean): Promise<any>;
  };

  // 工作区服务
  workspaceService?: {
    getWorkspace(): any;
    getWorkspaceFolder(resource: any): any;
  };

  // 用户信息监听服务
  userInfoWatcherService?: {
    getAndWatchUserInfo(callback: (userinfo: any) => void): void;
    removeUserInfoWatcher(callback: (userinfo: any) => void): void;
    getCurrentUserInfo(): any;
  };

  loginService?: {
    login(): Promise<void>;
  };

  // 代码 diff
  editCodeService?: {
    keepDiffExport: (payload?: { filepath?: string; abortChat?: boolean }) => Promise<void>;
    undoDiffExport: (payload?: { filepath?: string; abortChat?: boolean }) => Promise<void>;
    resetAllDiffsExport: () => Promise<void>;
    isPartialRejectExport: (filepath: string) => boolean;
    isPartialAcceptExport: (filepath: string) => boolean;
    getPartialAcceptPathsExport: () => string[];
    getPartialRejectPathsExport: () => string[];
  };

  sideActionService?: ISideActionService;
  radar?: Radar;

  // 扩展方法：通用服务获取器（作为备用方案）
  getService?<T>(serviceToken: any): T | undefined;
}

/**
 * 挂载应用到指定DOM元素
 *
 * @param rootElement - 要挂载应用的DOM元素
 * @param bridge - 桥接对象实现
 * @param accessor - 可选的 VSCode services accessor，用于访问 VSCode 服务
 * @returns 包含重新渲染和销毁方法的对象
 */
const mountApp = async (
  rootElement: HTMLElement,
  bridge: VSCodeNativeBridge,
  accessor?: VSCodeServicesAccessor,
) => {
  if (bridge) {
    try {
      await initHighlighterInstance();
    }
    catch (error) {
      console.error("mountApp: 初始化高亮器失败", error);
    }
    try {
      kwaiPilotBridgeAPI.setBridge(bridge);
      // 如果提供了 accessor，则将其注入到 bridge 中
      if (accessor) {
        kwaiPilotBridgeAPI.setServicesAccessor?.(accessor);
        console.log("mountApp: VSCode services accessor 已成功注入");

        const startTimestamp = Date.now();
        kwaiPilotBridgeAPI.getSession({
          page: 1,
          pageSize: 50,
          timeRange: "all",
        }).then(() => {
          accessor?.radar?.event({
            name: "GET_SESSION",
            event_type: "performance",
            category: "kwaipilot",
            result_type: "success",
            extra_info: {
              timestamp: startTimestamp,
            },
          }, {
            duration: Date.now() - startTimestamp,
          });

          accessor?.radar?.fmp();
        });
      }
      console.log("mountApp: bridge实现已成功注入");
    }
    catch (error) {
      console.error("mountApp: bridge实现注入失败", error);
    }
  }
  else {
    console.error("mountApp: 未提供bridge实现，API调用会失败");
  }

  import("./mountApp-export").then(({ default: mountApp }) => {
    mountApp(rootElement, bridge, accessor);
  });
};

export { mountApp };

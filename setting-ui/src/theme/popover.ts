import { popoverAnatomy as parts } from "@chakra-ui/anatomy";
import { createMultiStyleConfigHelpers } from "@chakra-ui/react";

const { definePartsStyle, defineMultiStyleConfig } = createMultiStyleConfigHelpers(parts.keys);

const baseStyle = definePartsStyle({
  // define the part you're going to style
  body: {
    // bg: "red", // change the background of the body to gray.800
  },
  content: {
    padding: 0, // change the padding of the content
    border: "none",
    boxShadow: "0px 2px 16px 0px var(--vscode-widget-shadow)",
    bg: "var(--vscode-listFilterWidget-background)",
  },
});

export default defineMultiStyleConfig({ baseStyle });

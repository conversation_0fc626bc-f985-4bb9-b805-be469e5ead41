import { kwaiPilotBridgeAPI } from "@/bridge";
import { useEffect, useMemo, useState } from "react";
import { Icon } from "@iconify/react";
import { SettingDesc } from "./SettingDesc";
import { Button } from "@chakra-ui/react";
import { IndexState } from "shared";
import { propertyTitleClassName, descriptionClassName, borderClassName } from "../schema/common";
import { shortenPathString } from "../utils/string";

export const FileIndexManualBuild = () => {
  const [progress, setProgress] = useState(0);
  const [isStarted, setIsStarted] = useState(false);
  const buildFinish = useMemo(() => progress >= 1, [progress]);
  const [message, setMessage] = useState("");
  const [paused, setPaused] = useState(false);
  const [isRepo, setIsRepo] = useState(true);
  const [buildStatus, setBuildStatus] = useState<IndexState["status"]>("paused");
  // progress保留到小数点后两位
  const showProgress = useMemo(() => (progress * 100).toFixed(0), [progress]);
  const [lastBuildTime, setLastBuildTime] = useState("");

  const startBuildIndex = async () => {
    if (!isRepo) {
      return;
    }
    await kwaiPilotBridgeAPI.extensionIndexFile.$startBuildIndex();
  };
  const deleteIndex = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$deleteIndex();
  };
  const stopIndexBuild = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$stopIndex();
  };

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.indexState().subscribe((state) => {
      setProgress(state.indexingProgress);
      setIsStarted(state.indexing);
      setLastBuildTime(state.lastBuildTime);
      setMessage(state.indexingMessage);
      // 修复暂停状态逻辑：只要status是paused就显示暂停状态
      setPaused(state.status === "paused" && state.pauseIndexManual);
      setBuildStatus(state.status);
    });

    return () => {
      sus.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const fetchIsRepo = async () => {
      const isRepo = await kwaiPilotBridgeAPI.extensionIndexFile.$getIsRepo();
      if (!isRepo) {
        setBuildStatus("error");
      }
      setIsRepo(isRepo);
    };

    fetchIsRepo();
  }, []);

  return (
    <>
      <div className={"pb-[16px] " + borderClassName}>
        <div className="flex justify-between gap-[40px]">
          <div>
            <div className={propertyTitleClassName}>代码索引</div>
            <div className={descriptionClassName}>
              构建仓库代码的全局索引，当发起智能体会话时将自动检索问题相关上下文，提升代码问答准确性
            </div>
          </div>
          <div className="flex gap-3 h-[34px]">
            {!isStarted && !buildFinish && (
              <Button
                disabled={!isRepo}
                variant="blueSolid"
                onClick={
                  startBuildIndex
                }
              >
                开始构建
              </Button>
            )}
            { isStarted && !buildFinish
            && (
              <Button
                onClick={stopIndexBuild}
                variant="blueSolid"
                leftIcon={<Icon icon="codicon:close"></Icon>}
              >
                取消构建
              </Button>
            )}
            {buildFinish && (
              <Button onClick={startBuildIndex} leftIcon={<Icon icon="codicon:debug-restart"></Icon>}>
                重新构建
              </Button>
            )}
            {buildFinish && (
              <Button
                onClick={deleteIndex}
                leftIcon={<Icon icon="material-symbols:delete-outline"></Icon>}
              >
                删除索引
              </Button>
            )}
          </div>
        </div>
        <div className="pt-4 pb-2">
          <div className="w-full h-1.5 bg-[var(--vscode-list-inactiveSelectionBackground)] rounded-[3px]">
            <div className={`w-full h-full  rounded-[3px] ${buildFinish ? "bg-[#00C2A5ff]" : "bg-[var(--vscode-progressBar-background)]"}`} style={{ width: `${showProgress}%` }}></div>
          </div>
        </div>
        <div className="flex justify-between gap-3">
          <SettingDesc>
            {
              buildStatus === "error"
                ? <Icon icon="ix:error-filled" className="text-[#E35151]" />
                : buildStatus === "indexed"
                  ? <Icon icon="mdi:success-circle" className="text-[#00C2A5ff]"></Icon>
                  : ""
            }
            {!isRepo
              ? "不是git仓库"
              : paused
                ? "已暂停索引"
                : message
                  ? <span title={message} className="inline-block whitespace-nowrap overflow-hidden text-ellipsis">{shortenPathString(message)}</span>
                  : buildFinish
                    ? `构建成功 ${lastBuildTime}`
                    : isStarted ? "构建索引中..." : "当前未构建索引"}
          </SettingDesc>
          <div>
            {showProgress}
            %
          </div>
        </div>
      </div>
    </>
  );
};

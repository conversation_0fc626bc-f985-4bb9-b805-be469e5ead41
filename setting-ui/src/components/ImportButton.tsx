import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>over<PERSON>rrow } from "@chakra-ui/react";
import { useState } from "react";
import { Icon } from "@iconify/react";

interface Props {
  productName: string;
  onConfirm: () => Promise<void>;
}

export default function ImportButton({ productName, onConfirm }: Props) {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const handlerClick = async () => {
    setLoading(true);
    setOpen(false);
    await onConfirm();
    setLoading(false);
  };
  if (loading) return <Button isLoading={loading} loadingText="导入中..."></Button>;

  return (
    <Popover isOpen={open} onClose={() => setOpen(false)} preventOverflow={false} strategy="fixed" placement="bottom-end">
      <PopoverTrigger>
        <Button onClick={() => setOpen(true)}>
          导入
          {" "}
          {productName}
          {" "}
          配置
        </Button>
      </PopoverTrigger>
      <PopoverContent bg="var(--vscode-editor-background)" padding="16px" borderRadius="4px" display="flex" flexDirection="column" minWidth="0" border="1px solid var(--vscode-pickerGroup-border)">
        {/* <PopoverArrow bg="var(--vscode-editor-background)" boxShadow="none" border="1px solid var(--vscode-pickerGroup-border)" borderTop="none" borderLeft="none" /> */}
        <div className="w-full flex items-center text-[var(--vscode-foreground)] text-[13px] font-[600] leading-[18px] mb-[8px] gap-[4px]">
          <Icon icon="codicon:info" fontSize="16px" />
          <span>{`确认导入 ${productName} 配置吗？`}</span>
        </div>
        <div className="w-full text-[var(--vscode-foreground)] text-[13px] leading-[18px] mb-[16px] text-left">导入后将覆盖 Kwaipilot 当前配置，且不可恢复</div>
        <div className="w-full flex justify-end gap-[8px]">
          <Button onClick={() => setOpen(false)}>取消</Button>
          <Button variant="blueSolid" onClick={handlerClick}>确认</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

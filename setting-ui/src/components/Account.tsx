import { Button } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "../bridge";
import { UserInfo } from "@shared/types";
import { useState, useEffect } from "react";
import { propertyTitleClassName, descriptionClassName } from "../schema/common";
import { getCurrentEnvIsInIDE } from "@/utils/ide";

export const RenderAccount = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const host = getCurrentEnvIsInIDE() ? "ide" : "plugin";

  const logout = async () => {
    kwaiPilotBridgeAPI.extensionSettings.$logout(host);
  };

  const login = () => {
    kwaiPilotBridgeAPI.extensionSettings.$login(host);
  };

  useEffect(() => {
    kwaiPilotBridgeAPI.getAndWatchUserInfo((userInfo) => {
      setUserInfo(userInfo ?? null);
    });
  }, []);

  return (
    <div className="flex items-center justify-between">
      <div className={"flex flex-col gap-[4px]" + propertyTitleClassName}>
        <div>
          账号设置
        </div>
        <div className={"flex align-center" + descriptionClassName}>
          当前登录账号：
          {userInfo?.displayName ?? "未登录"}
        </div>
      </div>
      {userInfo
        ? <Button onClick={logout}>注销</Button>
        : <Button onClick={login}>登录</Button>}
    </div>
  );
};

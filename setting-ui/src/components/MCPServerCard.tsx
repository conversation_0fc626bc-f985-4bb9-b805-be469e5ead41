import { Icon } from "@iconify/react";
import { McpServer } from "shared/lib/mcp/types";
import { VscodeLabel } from "@vscode-elements/react-elements";
import clsx from "clsx";
import { MCPSwitch } from "./MCPSwitch";
import { useAsyncFn } from "react-use";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useMemo } from "react";
import { useMcpStore } from "../store/useMcpStore";
import { ReportOpt } from "@shared/types/logger";
import { Tooltip, Popover, PopoverContent, PopoverTrigger } from "@chakra-ui/react";

export interface MCPServerCardProps extends McpServer {
  onEdit: () => void;
}

const getStatusInfo = (status: McpServer["status"]): { color: string; text: string } => {
  const statusMap: Record<McpServer["status"], { color: string; text: string }> = {
    connected: { color: "#73C991ff", text: "可使用" },
    connecting: { color: "#CCA700ff", text: "准备中" },
    disconnected: { color: "#C74E39ff", text: "不可使用" },
  };
  return statusMap[status];
};

export const MCPServerCard = (props: MCPServerCardProps) => {
  const {
    name,
    status,
    disabled,
    tools = [],
    onEdit,
    error,
    fullError,
  } = props;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, handleToggleServer] = useAsyncFn(async (serverName: string, disabled: boolean) => {
    await kwaiPilotBridgeAPI.extensionMCP.$toggleMcpServer({
      serverName,
      disabled,
    });
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [__, handleDeleteServer] = useAsyncFn(async (serverName: string) => {
    await kwaiPilotBridgeAPI.extensionMCP.$deleteMcpServer({
      serverName,
    });
  }, []);
  const [refreshServerState, handleRefreshServer] = useAsyncFn(async (serverName: string) => {
    const param: ReportOpt<"mcp_action"> = {
      key: "mcp_action",
      type: "mcp_setting_editmcp",
      content: serverName,
    };
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    await kwaiPilotBridgeAPI.extensionMCP.$restartMcpServer({
      serverName,
    });
  }, []);

  const isRefreshLoading = useMemo(() => {
    return refreshServerState.loading || status === "connecting";
  }, [refreshServerState.loading, status]);

  const renderToolsList = useMemo(() => {
    if (tools.length === 0) {
      return <span className="text-[12px] text-[var(--vscode-descriptionForeground)]">--</span>;
    }
    return tools.map((tool) => {
      const properties = (tool.inputSchema as unknown as { properties: Record<string, any> })?.properties || {};
      const required = (tool.inputSchema as unknown as { required: string[] })?.required || [];
      return (
        <Popover key={tool.name} trigger="hover">
          <PopoverTrigger>
            <span
              className="px-1.5 py-0.5 rounded text-[12px] cursor-pointer font-medium bg-[var(--vscode-badge-background)] text-[var(--vscode-badge-foreground)] hover:text-[var(--vscode-text-foreground)]"
            >
              {tool.name}
            </span>
          </PopoverTrigger>
          <PopoverContent>
            <div className="max-h-[246px] overflow-auto text-[12px]">
              { tool.description
                ? (
                    <div className="p-1 border-b border-[var(--vscode-widget-border)]">
                      {tool.description}
                    </div>
                  )
                : null}
              {
                Object.entries(properties).length > 0 && (
                  <div className="p-[12px]">
                    <div className="mb-1">参数</div>
                    {Object.entries(properties).map(([key, value]) => (
                      <div className="flex mt-1" key={key}>
                        <div className="mr-[4px] font-bold min-w-[60px] text-right">
                          {
                            required?.includes(key) && (
                              <span className="text-[#C74E39ff] mr-1">*</span>
                            )
                          }
                          {`${key}:`}
                        </div>
                        <div className="break-all">{(value as any)?.description || "--"}</div>
                      </div>
                    ))}
                  </div>
                )
              }
            </div>
          </PopoverContent>
        </Popover>
      );
    });
  }, [tools]);

  const renderError = useMemo(() => {
    if (!error) return null;
    return (
      <span className="text-[12px] mt-[10px] text-[#C74E39ff] break-all flex items-center">
        {error}
        { fullError && <Popover trigger="hover">
          <PopoverTrigger>
          <Icon icon="codicon:question" className="size-[16px] text-[var(--vscode-foreground)] ml-1 cursor-pointer flex-shrink-0" />
          </PopoverTrigger>
          <PopoverContent>
            <div className="text-[12px] text-[#C74E39ff] whitespace-pre-wrap max-h-[300px] overflow-y-auto">{`${fullError}`}</div>
          </PopoverContent>
          </Popover>
        }
      </span>
    );
  }, [error, fullError]);
  return (
    <div className="flex flex-col mt-4 p-4 rounded border border-[var(--vscode-widget-border)]">
      <div className="flex items-center justify-between">
        <div className="flex items-center flex-1">
          <div className="text-[13px] font-semibold flex-1 break-all line-clamp-1">{name}</div>
          {
            !disabled && (
              <div className="flex items-center flex-shrink-0 mr-6">
                <div className="size-[4px] rounded-full mr-[5px] ml-[13px]" style={{ backgroundColor: getStatusInfo(status).color }} />
                <div className="text-[12px]" style={{ color: getStatusInfo(status).color }}>
                  {getStatusInfo(status).text}
                </div>
              </div>
            )
          }
        </div>
        <div className="flex items-center">
          <MCPSwitch
            checked={!disabled}
            onChange={(checked) => {
              handleToggleServer(name, !checked);
            }}
          />
          <div className="mx-3 h-[12px] w-[1px] bg-[var(--vscode-widget-border)]" />
          <Tooltip label="更新" placement="top" hasArrow>
            <div
              onClick={() => {
                if (isRefreshLoading) return;
                handleRefreshServer(name);
              }}
              className={clsx(
                "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)]",
                {
                  "opacity-50 cursor-not-allowed": isRefreshLoading,
                },
              )}
            >
              <Icon
                icon="codicon:refresh"
                className={clsx("size-[16px] text-[var(--vscode-foreground)]", {
                  "animate-spin": isRefreshLoading,
                })}
              />
            </div>
          </Tooltip>
          <Tooltip label="编辑" placement="top" hasArrow>
            <div
              onClick={(onEdit)}
              className={clsx(
                "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)] mx-1",
              )}
            >
              <Icon icon="codicon:edit" className="size-[16px] text-[var(--vscode-foreground)]" />
            </div>
          </Tooltip>
          <Tooltip label="删除" placement="top" hasArrow>
            <div
              onClick={() => handleDeleteServer(name)}
              className={clsx(
                "w-[22px] h-[22px] cursor-pointer rounded flex items-center justify-center hover:bg-[var(--vscode-widget-border)]",
              )}
            >
              <Icon icon="codicon:trash" className="size-[16px] text-[var(--vscode-foreground)]" />
            </div>
          </Tooltip>
        </div>
      </div>
      {
        !disabled && (
          <div className="flex items-center mt-3">
            <VscodeLabel className="text-[12px] mr-2">Tools:</VscodeLabel>
            <div className="flex flex-wrap gap-1">
              {renderToolsList}
            </div>
          </div>
        )
      }
      {renderError}
    </div>
  );
};
